@echo off
setlocal enabledelayedexpansion

:: ========================================
::    HWID TOOLKIT - Suite Complete
:: ========================================
:: Menu principal pour tous les outils HWID
:: ========================================

title HWID Toolkit - Suite Complete

:: Couleurs
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "MAGENTA=[95m"
set "CYAN=[96m"
set "WHITE=[97m"
set "RESET=[0m"

:MainMenu
cls
echo %CYAN%========================================%RESET%
echo %CYAN%         HWID TOOLKIT v2.0%RESET%
echo %CYAN%    Suite Complete d'Outils HWID%RESET%
echo %CYAN%========================================%RESET%
echo.
echo %RED%⚠️  ATTENTION: Utilisation educative uniquement%RESET%
echo %RED%⚠️  L'utilisation malveillante est interdite%RESET%
echo.
echo %WHITE%Choisissez une option:%RESET%
echo.
echo %GREEN%[1]%RESET% %YELLOW%Changer TOUS les identifiants HWID%RESET%
echo %GREEN%[2]%RESET% %BLUE%Verifier les identifiants actuels%RESET%
echo %GREEN%[3]%RESET% %MAGENTA%Restaurer depuis une sauvegarde%RESET%
echo %GREEN%[4]%RESET% %CYAN%Informations sur les outils%RESET%
echo %GREEN%[5]%RESET% %WHITE%Quitter%RESET%
echo.

:: Vérifier les privilèges administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo %RED%⚠️  Privileges administrateur requis pour les modifications%RESET%
    echo %YELLOW%   Clic droit sur ce script ^> "Executer en tant qu'administrateur"%RESET%
    echo.
)

set /p "choice=Votre choix (1-5): "

if "%choice%"=="1" goto :ChangeHWID
if "%choice%"=="2" goto :CheckHWID
if "%choice%"=="3" goto :RestoreHWID
if "%choice%"=="4" goto :ShowInfo
if "%choice%"=="5" goto :Exit

echo %RED%Choix invalide. Veuillez entrer un numero entre 1 et 5.%RESET%
timeout /t 2 >nul
goto :MainMenu

:: ========================================
:: OPTION 1: CHANGER TOUS LES HWID
:: ========================================
:ChangeHWID
cls
echo %CYAN%========================================%RESET%
echo %CYAN%    MODIFICATION COMPLETE DES HWID%RESET%
echo %CYAN%========================================%RESET%
echo.

:: Vérifier les privilèges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo %RED%ERREUR: Privileges administrateur requis%RESET%
    echo %YELLOW%Relancez ce script en tant qu'administrateur%RESET%
    pause
    goto :MainMenu
)

echo %YELLOW%Ce script va modifier TOUS vos identifiants hardware:%RESET%
echo.
echo %WHITE%• Machine GUID%RESET%
echo %WHITE%• Nom d'ordinateur%RESET%
echo %WHITE%• Product ID Windows%RESET%
echo %WHITE%• Proprietaire enregistre%RESET%
echo %WHITE%• Adresses MAC%RESET%
echo %WHITE%• Identifiants de disques%RESET%
echo %WHITE%• Identifiants USB%RESET%
echo.
echo %RED%⚠️  CES MODIFICATIONS SONT PERMANENTES !%RESET%
echo %GREEN%✓ Une sauvegarde sera creee automatiquement%RESET%
echo.

set /p "confirm=Etes-vous ABSOLUMENT sur ? (tapez OUI): "
if /i not "%confirm%"=="OUI" (
    echo %YELLOW%Operation annulee%RESET%
    pause
    goto :MainMenu
)

echo.
echo %CYAN%Lancement du script de modification...%RESET%
call hwid_changer_complete.cmd
pause
goto :MainMenu

:: ========================================
:: OPTION 2: VERIFIER LES HWID
:: ========================================
:CheckHWID
cls
echo %CYAN%========================================%RESET%
echo %CYAN%    VERIFICATION DES IDENTIFIANTS%RESET%
echo %CYAN%========================================%RESET%
echo.

echo %BLUE%Lancement de la verification...%RESET%
call hwid_check.cmd
goto :MainMenu

:: ========================================
:: OPTION 3: RESTAURER LES HWID
:: ========================================
:RestoreHWID
cls
echo %CYAN%========================================%RESET%
echo %CYAN%    RESTAURATION DES IDENTIFIANTS%RESET%
echo %CYAN%========================================%RESET%
echo.

:: Vérifier les privilèges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo %RED%ERREUR: Privileges administrateur requis%RESET%
    echo %YELLOW%Relancez ce script en tant qu'administrateur%RESET%
    pause
    goto :MainMenu
)

echo %MAGENTA%Lancement de la restauration...%RESET%
call hwid_restore.cmd
pause
goto :MainMenu

:: ========================================
:: OPTION 4: INFORMATIONS
:: ========================================
:ShowInfo
cls
echo %CYAN%========================================%RESET%
echo %CYAN%    INFORMATIONS SUR LES OUTILS%RESET%
echo %CYAN%========================================%RESET%
echo.

echo %YELLOW%=== HWID TOOLKIT v2.0 ===%RESET%
echo.
echo %WHITE%Cette suite d'outils permet de:%RESET%
echo.
echo %GREEN%✓ Modifier tous les identifiants hardware%RESET%
echo %GREEN%✓ Verifier les identifiants actuels%RESET%
echo %GREEN%✓ Restaurer depuis une sauvegarde%RESET%
echo %GREEN%✓ Creer des sauvegardes automatiques%RESET%
echo.

echo %YELLOW%=== IDENTIFIANTS MODIFIES ===%RESET%
echo.
echo %WHITE%• Machine GUID (Windows)%RESET%
echo %WHITE%• Nom d'ordinateur%RESET%
echo %WHITE%• Product ID Windows%RESET%
echo %WHITE%• Proprietaire enregistre%RESET%
echo %WHITE%• Adresses MAC des cartes reseau%RESET%
echo %WHITE%• Noms des peripheriques de stockage%RESET%
echo %WHITE%• Noms des peripheriques USB%RESET%
echo.

echo %YELLOW%=== SECURITE ===%RESET%
echo.
echo %GREEN%✓ Sauvegardes automatiques avant modification%RESET%
echo %GREEN%✓ Possibilite de restauration complete%RESET%
echo %GREEN%✓ Verification des privileges administrateur%RESET%
echo %GREEN%✓ Rapports detailles generes%RESET%
echo.

echo %YELLOW%=== UTILISATION LEGALE ===%RESET%
echo.
echo %WHITE%Ces outils sont destines UNIQUEMENT a:%RESET%
echo %GREEN%✓ L'apprentissage de la securite informatique%RESET%
echo %GREEN%✓ Les tests sur vos propres systemes%RESET%
echo %GREEN%✓ La recherche academique%RESET%
echo.
echo %RED%❌ N'utilisez PAS pour:%RESET%
echo %RED%❌ Contourner des bans ou restrictions%RESET%
echo %RED%❌ Violer des conditions d'utilisation%RESET%
echo %RED%❌ Toute activite illegale%RESET%
echo.

echo %YELLOW%=== FICHIERS INCLUS ===%RESET%
echo.
echo %WHITE%• hwid_toolkit.cmd%RESET% %CYAN%(ce fichier - menu principal)%RESET%
echo %WHITE%• hwid_changer_complete.cmd%RESET% %CYAN%(modification complete)%RESET%
echo %WHITE%• hwid_check.cmd%RESET% %CYAN%(verification des identifiants)%RESET%
echo %WHITE%• hwid_restore.cmd%RESET% %CYAN%(restauration depuis sauvegarde)%RESET%
echo.

echo %YELLOW%=== SUPPORT ===%RESET%
echo.
echo %WHITE%En cas de probleme:%RESET%
echo %GREEN%• Verifiez les privileges administrateur%RESET%
echo %GREEN%• Desactivez temporairement l'antivirus%RESET%
echo %GREEN%• Consultez les sauvegardes sur le bureau%RESET%
echo %GREEN%• Redemarrez apres les modifications%RESET%
echo.

echo %CYAN%========================================%RESET%
pause
goto :MainMenu

:: ========================================
:: OPTION 5: QUITTER
:: ========================================
:Exit
cls
echo %CYAN%========================================%RESET%
echo %CYAN%         HWID TOOLKIT v2.0%RESET%
echo %CYAN%========================================%RESET%
echo.
echo %YELLOW%Merci d'avoir utilise HWID Toolkit%RESET%
echo.
echo %WHITE%Rappels importants:%RESET%
echo %GREEN%✓ Utilisez uniquement a des fins educatives%RESET%
echo %GREEN%✓ Respectez les lois et reglementations%RESET%
echo %GREEN%✓ Redemarrez apres les modifications%RESET%
echo.
echo %CYAN%Au revoir !%RESET%
echo.
timeout /t 3 >nul
exit /b 0
