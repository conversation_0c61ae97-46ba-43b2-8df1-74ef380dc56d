# Script de diagnostic pour vérifier les modifications HWID
# ATTENTION: Utilisation éducative uniquement

Write-Host "========================================" -ForegroundColor Yellow
Write-Host "    Diagnostic HWID - Vérification" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
Write-Host ""

# Vérifier si on est administrateur
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "ERREUR: Ce script doit être exécuté en tant qu'administrateur." -ForegroundColor Red
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

Write-Host "✓ Privilèges administrateur détectés" -ForegroundColor Green
Write-Host ""

# Fonction pour lire une valeur de registre
function Get-RegistryValue {
    param(
        [string]$Path,
        [string]$Name
    )
    
    try {
        $value = Get-ItemProperty -Path $Path -Name $Name -ErrorAction Stop
        return $value.$Name
    }
    catch {
        return "ERREUR: $($_.Exception.Message)"
    }
}

# Vérifier les identifiants principaux
Write-Host "=== IDENTIFIANTS HARDWARE ACTUELS ===" -ForegroundColor Cyan
Write-Host ""

# 1. Machine GUID
Write-Host "1. Machine GUID:" -ForegroundColor White
$machineGuid = Get-RegistryValue -Path "HKLM:\SOFTWARE\Microsoft\Cryptography" -Name "MachineGuid"
Write-Host "   $machineGuid" -ForegroundColor Yellow
Write-Host ""

# 2. Processeur ID
Write-Host "2. Processeur:" -ForegroundColor White
try {
    $cpuInfo = Get-WmiObject -Class Win32_Processor | Select-Object -First 1
    Write-Host "   ID: $($cpuInfo.ProcessorId)" -ForegroundColor Yellow
    Write-Host "   Nom: $($cpuInfo.Name)" -ForegroundColor Yellow
}
catch {
    Write-Host "   ERREUR: Impossible de lire les infos CPU" -ForegroundColor Red
}
Write-Host ""

# 3. Carte mère
Write-Host "3. Carte Mère:" -ForegroundColor White
try {
    $motherboard = Get-WmiObject -Class Win32_BaseBoard | Select-Object -First 1
    Write-Host "   Série: $($motherboard.SerialNumber)" -ForegroundColor Yellow
    Write-Host "   Produit: $($motherboard.Product)" -ForegroundColor Yellow
}
catch {
    Write-Host "   ERREUR: Impossible de lire les infos carte mère" -ForegroundColor Red
}
Write-Host ""

# 4. Disques
Write-Host "4. Disques:" -ForegroundColor White
try {
    $disks = Get-WmiObject -Class Win32_DiskDrive
    $diskIndex = 0
    foreach ($disk in $disks) {
        if ($diskIndex -lt 3) {  # Limiter à 3 disques
            Write-Host "   Disque $diskIndex - Série: $($disk.SerialNumber.Trim())" -ForegroundColor Yellow
            Write-Host "   Disque $diskIndex - Modèle: $($disk.Model)" -ForegroundColor Yellow
            $diskIndex++
        }
    }
}
catch {
    Write-Host "   ERREUR: Impossible de lire les infos disques" -ForegroundColor Red
}
Write-Host ""

# 5. Réseau
Write-Host "5. Cartes Réseau:" -ForegroundColor White
try {
    $adapters = Get-WmiObject -Class Win32_NetworkAdapter | Where-Object { $_.PhysicalAdapter -eq $true -and $_.MACAddress -ne $null }
    $adapterIndex = 0
    foreach ($adapter in $adapters) {
        if ($adapterIndex -lt 2) {  # Limiter à 2 adaptateurs
            Write-Host "   Adaptateur $adapterIndex - MAC: $($adapter.MACAddress)" -ForegroundColor Yellow
            Write-Host "   Adaptateur $adapterIndex - Nom: $($adapter.Name)" -ForegroundColor Yellow
            $adapterIndex++
        }
    }
}
catch {
    Write-Host "   ERREUR: Impossible de lire les infos réseau" -ForegroundColor Red
}
Write-Host ""

# 6. BIOS
Write-Host "6. BIOS:" -ForegroundColor White
try {
    $bios = Get-WmiObject -Class Win32_BIOS | Select-Object -First 1
    Write-Host "   Série: $($bios.SerialNumber)" -ForegroundColor Yellow
    Write-Host "   Version: $($bios.SMBIOSBIOSVersion)" -ForegroundColor Yellow
}
catch {
    Write-Host "   ERREUR: Impossible de lire les infos BIOS" -ForegroundColor Red
}
Write-Host ""

# Vérifier les sauvegardes HWID Spoofer
Write-Host "=== SAUVEGARDES HWID SPOOFER ===" -ForegroundColor Cyan
$backupPath = "$env:APPDATA\HwidSpoofer\Backups"
if (Test-Path $backupPath) {
    $backups = Get-ChildItem -Path $backupPath -Filter "backup_*.json" | Sort-Object LastWriteTime -Descending
    if ($backups.Count -gt 0) {
        Write-Host "Sauvegardes trouvées:" -ForegroundColor Green
        foreach ($backup in $backups | Select-Object -First 5) {
            Write-Host "   $($backup.Name) - $($backup.LastWriteTime)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "Aucune sauvegarde trouvée" -ForegroundColor Orange
    }
} else {
    Write-Host "Dossier de sauvegarde non trouvé: $backupPath" -ForegroundColor Orange
}
Write-Host ""

# Instructions
Write-Host "=== INSTRUCTIONS ===" -ForegroundColor Cyan
Write-Host "1. Comparez ces valeurs avec celles affichées dans HWID Spoofer" -ForegroundColor White
Write-Host "2. Si elles sont identiques, les modifications ne se sont pas appliquées" -ForegroundColor White
Write-Host "3. Si elles sont différentes, les modifications ont fonctionné" -ForegroundColor White
Write-Host "4. Certains changements nécessitent un redémarrage complet" -ForegroundColor White
Write-Host ""

Write-Host "=== SOLUTIONS POSSIBLES ===" -ForegroundColor Cyan
Write-Host "• Relancer HWID Spoofer en tant qu'administrateur" -ForegroundColor White
Write-Host "• Vérifier les logs dans l'onglet 'Logs & Activité'" -ForegroundColor White
Write-Host "• Redémarrer complètement l'ordinateur" -ForegroundColor White
Write-Host "• Désactiver temporairement l'antivirus" -ForegroundColor White
Write-Host ""

Read-Host "Appuyez sur Entrée pour quitter"
