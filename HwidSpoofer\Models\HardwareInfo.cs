using System;
using System.Collections.Generic;

namespace HwidSpoofer.Models
{
    /// <summary>
    /// Représente les informations d'un composant hardware
    /// </summary>
    public class HardwareInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string CurrentValue { get; set; } = string.Empty;
        public string OriginalValue { get; set; } = string.Empty;
        public string RegistryPath { get; set; } = string.Empty;
        public string RegistryKey { get; set; } = string.Empty;
        public bool IsModified { get; set; } = false;
        public bool CanBeModified { get; set; } = true;
        public DateTime LastModified { get; set; } = DateTime.MinValue;

        public HardwareInfo()
        {
        }

        public HardwareInfo(string name, string category, string currentValue, string registryPath, string registryKey)
        {
            Name = name;
            Category = category;
            CurrentValue = currentValue;
            OriginalValue = currentValue;
            RegistryPath = registryPath;
            RegistryKey = registryKey;
        }

        /// <summary>
        /// Indique si la valeur actuelle diffère de la valeur originale
        /// </summary>
        public bool HasChanged => CurrentValue != OriginalValue;

        /// <summary>
        /// Remet la valeur actuelle à la valeur originale
        /// </summary>
        public void RestoreOriginal()
        {
            CurrentValue = OriginalValue;
            IsModified = false;
            LastModified = DateTime.MinValue;
        }

        /// <summary>
        /// Met à jour la valeur actuelle
        /// </summary>
        public void UpdateValue(string newValue)
        {
            if (CurrentValue != newValue)
            {
                CurrentValue = newValue;
                IsModified = true;
                LastModified = DateTime.Now;
            }
        }

        public override string ToString()
        {
            return $"{Name}: {CurrentValue} {(IsModified ? "(Modifié)" : "")}";
        }
    }

    /// <summary>
    /// Représente une sauvegarde complète des informations hardware
    /// </summary>
    public class HardwareBackup
    {
        public DateTime CreatedAt { get; set; }
        public string Description { get; set; } = string.Empty;
        public Dictionary<string, HardwareInfo> HardwareItems { get; set; } = new();
        public string SystemInfo { get; set; } = string.Empty;

        public HardwareBackup()
        {
            CreatedAt = DateTime.Now;
        }

        public HardwareBackup(string description) : this()
        {
            Description = description;
        }
    }

    /// <summary>
    /// Énumération des catégories de hardware supportées
    /// </summary>
    public enum HardwareCategory
    {
        Processor,
        Motherboard,
        Storage,
        Network,
        BIOS,
        System,
        Memory,
        Graphics
    }

    /// <summary>
    /// Résultat d'une opération sur le hardware
    /// </summary>
    public class HardwareOperationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public Exception? Exception { get; set; }
        public List<string> Warnings { get; set; } = new();

        public HardwareOperationResult(bool success, string message)
        {
            Success = success;
            Message = message;
        }

        public static HardwareOperationResult CreateSuccess(string message = "Opération réussie")
        {
            return new HardwareOperationResult(true, message);
        }

        public static HardwareOperationResult CreateError(string message, Exception? exception = null)
        {
            return new HardwareOperationResult(false, message) { Exception = exception };
        }

        public void AddWarning(string warning)
        {
            Warnings.Add(warning);
        }
    }
}
