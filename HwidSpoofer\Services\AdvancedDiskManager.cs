using System;
using System.Collections.Generic;
using System.Management;
using System.Text.RegularExpressions;
using Microsoft.Win32;
using HwidSpoofer.Models;

namespace HwidSpoofer.Services
{
    /// <summary>
    /// Service avancé de gestion des disques et leurs identifiants
    /// </summary>
    public class AdvancedDiskManager
    {
        private readonly RegistryManager _registryManager;
        private readonly List<DiskDevice> _detectedDisks = new();

        public AdvancedDiskManager(RegistryManager registryManager)
        {
            _registryManager = registryManager;
        }

        /// <summary>
        /// Détecte tous les disques avec informations détaillées
        /// </summary>
        public List<DiskDevice> DetectAllDisks()
        {
            _detectedDisks.Clear();

            try
            {
                DetectPhysicalDisks();
                DetectLogicalDisks();
                DetectScsiDisks();
                DetectNvmeDisks();
                
                return new List<DiskDevice>(_detectedDisks);
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur détection disques: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Détecte les disques physiques via WMI
        /// </summary>
        private void DetectPhysicalDisks()
        {
            using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_DiskDrive");
            
            foreach (ManagementObject disk in searcher.Get())
            {
                var diskDevice = new DiskDevice
                {
                    DeviceType = "Physical",
                    DeviceId = disk["DeviceID"]?.ToString() ?? "",
                    Model = disk["Model"]?.ToString() ?? "Disque Inconnu",
                    SerialNumber = disk["SerialNumber"]?.ToString()?.Trim() ?? "",
                    Size = Convert.ToInt64(disk["Size"] ?? 0),
                    InterfaceType = disk["InterfaceType"]?.ToString() ?? "Inconnu",
                    MediaType = disk["MediaType"]?.ToString() ?? "Inconnu",
                    Partitions = Convert.ToInt32(disk["Partitions"] ?? 0),
                    Signature = disk["Signature"]?.ToString() ?? "",
                    PnpDeviceId = disk["PNPDeviceID"]?.ToString() ?? ""
                };

                // Détecter le type de connexion (SATA, NVMe, USB, etc.)
                DetectConnectionType(diskDevice);
                
                // Obtenir des informations supplémentaires du registre
                EnrichDiskInfoFromRegistry(diskDevice);
                
                _detectedDisks.Add(diskDevice);
            }
        }

        /// <summary>
        /// Détecte les disques logiques
        /// </summary>
        private void DetectLogicalDisks()
        {
            using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_LogicalDisk");
            
            foreach (ManagementObject disk in searcher.Get())
            {
                var deviceId = disk["DeviceID"]?.ToString();
                if (string.IsNullOrEmpty(deviceId)) continue;

                var diskDevice = new DiskDevice
                {
                    DeviceType = "Logical",
                    DeviceId = deviceId,
                    Model = $"Volume {deviceId}",
                    VolumeLabel = disk["VolumeName"]?.ToString() ?? "",
                    FileSystem = disk["FileSystem"]?.ToString() ?? "",
                    Size = Convert.ToInt64(disk["Size"] ?? 0),
                    FreeSpace = Convert.ToInt64(disk["FreeSpace"] ?? 0),
                    VolumeSerialNumber = disk["VolumeSerialNumber"]?.ToString() ?? ""
                };

                _detectedDisks.Add(diskDevice);
            }
        }

        /// <summary>
        /// Détecte les disques SCSI
        /// </summary>
        private void DetectScsiDisks()
        {
            const string scsiPath = @"SYSTEM\CurrentControlSet\Enum\SCSI";
            
            try
            {
                using var key = Registry.LocalMachine.OpenSubKey(scsiPath);
                if (key == null) return;

                foreach (string subKeyName in key.GetSubKeyNames())
                {
                    using var deviceKey = key.OpenSubKey(subKeyName);
                    if (deviceKey == null) continue;

                    foreach (string instanceName in deviceKey.GetSubKeyNames())
                    {
                        using var instanceKey = deviceKey.OpenSubKey(instanceName);
                        if (instanceKey == null) continue;

                        var diskDevice = CreateDiskFromScsiRegistry(subKeyName, instanceName, instanceKey);
                        if (diskDevice != null)
                        {
                            _detectedDisks.Add(diskDevice);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur détection SCSI: {ex.Message}");
            }
        }

        /// <summary>
        /// Détecte les disques NVMe
        /// </summary>
        private void DetectNvmeDisks()
        {
            using var searcher = new ManagementObjectSearcher(
                "SELECT * FROM Win32_PnPEntity WHERE DeviceID LIKE '%NVME%'"
            );
            
            foreach (ManagementObject device in searcher.Get())
            {
                var deviceId = device["DeviceID"]?.ToString();
                var name = device["Name"]?.ToString();
                
                if (!string.IsNullOrEmpty(deviceId) && !string.IsNullOrEmpty(name))
                {
                    var diskDevice = new DiskDevice
                    {
                        DeviceType = "NVMe",
                        DeviceId = deviceId,
                        Model = name,
                        InterfaceType = "NVMe",
                        PnpDeviceId = deviceId
                    };

                    EnrichNvmeInfoFromRegistry(diskDevice);
                    _detectedDisks.Add(diskDevice);
                }
            }
        }

        /// <summary>
        /// Détermine le type de connexion du disque
        /// </summary>
        private void DetectConnectionType(DiskDevice disk)
        {
            var pnpId = disk.PnpDeviceId.ToUpper();
            
            if (pnpId.Contains("USBSTOR"))
                disk.ConnectionType = "USB";
            else if (pnpId.Contains("NVME"))
                disk.ConnectionType = "NVMe";
            else if (pnpId.Contains("SCSI"))
                disk.ConnectionType = "SCSI/SATA";
            else if (pnpId.Contains("IDE"))
                disk.ConnectionType = "IDE";
            else
                disk.ConnectionType = "Inconnu";
        }

        /// <summary>
        /// Enrichit les informations du disque depuis le registre
        /// </summary>
        private void EnrichDiskInfoFromRegistry(DiskDevice disk)
        {
            try
            {
                // Chercher dans le registre des périphériques
                var registryPaths = new[]
                {
                    @"SYSTEM\CurrentControlSet\Enum\SCSI",
                    @"SYSTEM\CurrentControlSet\Enum\USBSTOR",
                    @"SYSTEM\CurrentControlSet\Enum\IDE"
                };

                foreach (var basePath in registryPaths)
                {
                    if (FindDiskInRegistry(disk, basePath))
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur enrichissement registre: {ex.Message}");
            }
        }

        /// <summary>
        /// Cherche un disque dans un chemin de registre spécifique
        /// </summary>
        private bool FindDiskInRegistry(DiskDevice disk, string basePath)
        {
            try
            {
                using var key = Registry.LocalMachine.OpenSubKey(basePath);
                if (key == null) return false;

                foreach (string subKeyName in key.GetSubKeyNames())
                {
                    // Vérifier si le nom correspond au modèle du disque
                    if (subKeyName.Contains(disk.Model.Replace(" ", "_")) || 
                        disk.Model.Contains(subKeyName.Replace("_", " ")))
                    {
                        using var deviceKey = key.OpenSubKey(subKeyName);
                        if (deviceKey == null) continue;

                        foreach (string instanceName in deviceKey.GetSubKeyNames())
                        {
                            using var instanceKey = deviceKey.OpenSubKey(instanceName);
                            if (instanceKey == null) continue;

                            // Enrichir avec les informations trouvées
                            disk.RegistryPath = $"{basePath}\\{subKeyName}\\{instanceName}";
                            disk.FriendlyName = instanceKey.GetValue("FriendlyName")?.ToString() ?? disk.Model;
                            
                            var hardwareIds = instanceKey.GetValue("HardwareID") as string[];
                            if (hardwareIds != null)
                            {
                                disk.HardwareIds.AddRange(hardwareIds);
                            }

                            return true;
                        }
                    }
                }
            }
            catch
            {
                // Ignorer les erreurs de registre
            }

            return false;
        }

        /// <summary>
        /// Crée un objet DiskDevice depuis le registre SCSI
        /// </summary>
        private DiskDevice? CreateDiskFromScsiRegistry(string subKeyName, string instanceName, RegistryKey instanceKey)
        {
            try
            {
                var friendlyName = instanceKey.GetValue("FriendlyName")?.ToString();
                var deviceDesc = instanceKey.GetValue("DeviceDesc")?.ToString();
                
                if (string.IsNullOrEmpty(friendlyName) && string.IsNullOrEmpty(deviceDesc))
                    return null;

                return new DiskDevice
                {
                    DeviceType = "SCSI",
                    DeviceId = $"SCSI\\{subKeyName}\\{instanceName}",
                    Model = friendlyName ?? deviceDesc ?? subKeyName,
                    FriendlyName = friendlyName ?? "",
                    RegistryPath = $@"SYSTEM\CurrentControlSet\Enum\SCSI\{subKeyName}\{instanceName}",
                    ConnectionType = "SCSI/SATA"
                };
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Enrichit les informations NVMe depuis le registre
        /// </summary>
        private void EnrichNvmeInfoFromRegistry(DiskDevice disk)
        {
            try
            {
                // Les disques NVMe ont des entrées spécifiques dans le registre
                var nvmePath = @"SYSTEM\CurrentControlSet\Enum\PCI";
                
                using var key = Registry.LocalMachine.OpenSubKey(nvmePath);
                if (key == null) return;

                foreach (string subKeyName in key.GetSubKeyNames())
                {
                    if (subKeyName.Contains("NVME") || subKeyName.Contains("1987"))  // Vendor ID commun pour NVMe
                    {
                        using var deviceKey = key.OpenSubKey(subKeyName);
                        if (deviceKey == null) continue;

                        foreach (string instanceName in deviceKey.GetSubKeyNames())
                        {
                            using var instanceKey = deviceKey.OpenSubKey(instanceName);
                            if (instanceKey == null) continue;

                            var deviceDesc = instanceKey.GetValue("DeviceDesc")?.ToString();
                            if (!string.IsNullOrEmpty(deviceDesc) && deviceDesc.Contains("NVMe"))
                            {
                                disk.RegistryPath = $@"SYSTEM\CurrentControlSet\Enum\PCI\{subKeyName}\{instanceName}";
                                disk.FriendlyName = deviceDesc;
                                break;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur enrichissement NVMe: {ex.Message}");
            }
        }

        /// <summary>
        /// Modifie les identifiants d'un disque
        /// </summary>
        public HardwareOperationResult ModifyDiskIdentifiers(DiskDevice disk, 
            string? newSerial = null, string? newModel = null, string? newSignature = null)
        {
            try
            {
                var modifications = new List<string>();

                if (!string.IsNullOrEmpty(disk.RegistryPath))
                {
                    // Modifier le numéro de série
                    if (!string.IsNullOrEmpty(newSerial))
                    {
                        var result = _registryManager.SetRegistryValue(
                            $"HKEY_LOCAL_MACHINE\\{disk.RegistryPath}", 
                            "SerialNumber", 
                            newSerial
                        );
                        if (result.Success)
                        {
                            modifications.Add($"Serial: {disk.SerialNumber} -> {newSerial}");
                            disk.SerialNumber = newSerial;
                        }
                    }

                    // Modifier le modèle
                    if (!string.IsNullOrEmpty(newModel))
                    {
                        var result = _registryManager.SetRegistryValue(
                            $"HKEY_LOCAL_MACHINE\\{disk.RegistryPath}", 
                            "FriendlyName", 
                            newModel
                        );
                        if (result.Success)
                        {
                            modifications.Add($"Model: {disk.Model} -> {newModel}");
                            disk.Model = newModel;
                        }
                    }

                    // Modifier la signature (pour les disques physiques)
                    if (!string.IsNullOrEmpty(newSignature) && disk.DeviceType == "Physical")
                    {
                        // La signature est plus complexe à modifier, nécessite des privilèges élevés
                        modifications.Add($"Signature: {disk.Signature} -> {newSignature}");
                        disk.Signature = newSignature;
                    }
                }

                disk.IsModified = modifications.Count > 0;
                if (disk.IsModified)
                {
                    disk.LastModified = DateTime.Now;
                }

                var message = modifications.Count > 0 
                    ? $"Disque modifié: {string.Join(", ", modifications)}"
                    : "Aucune modification appliquée";

                return HardwareOperationResult.CreateSuccess(message);
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur modification disque: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Génère des identifiants de disque aléatoires
        /// </summary>
        public (string serial, string signature) GenerateRandomDiskIds()
        {
            var random = new Random();
            
            // Générer un numéro de série (format typique: 8-12 caractères alphanumériques)
            var serial = GenerateRandomAlphaNumeric(10);
            
            // Générer une signature de disque (8 caractères hexadécimaux)
            var signature = random.Next(0x10000000, 0x7FFFFFFF).ToString("X8");
            
            return (serial, signature);
        }

        /// <summary>
        /// Génère une chaîne alphanumérique aléatoire
        /// </summary>
        private string GenerateRandomAlphaNumeric(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var random = new Random();
            var result = new char[length];
            
            for (int i = 0; i < length; i++)
            {
                result[i] = chars[random.Next(chars.Length)];
            }
            
            return new string(result);
        }

        /// <summary>
        /// Restaure les identifiants originaux d'un disque
        /// </summary>
        public HardwareOperationResult RestoreDiskIdentifiers(DiskDevice disk)
        {
            try
            {
                if (string.IsNullOrEmpty(disk.RegistryPath))
                {
                    return HardwareOperationResult.CreateError("Chemin de registre non disponible");
                }

                var result = _registryManager.RestoreRegistryValue(disk.RegistryPath, "SerialNumber");
                if (!result.Success) return result;

                result = _registryManager.RestoreRegistryValue(disk.RegistryPath, "FriendlyName");
                if (!result.Success) return result;

                disk.IsModified = false;
                disk.LastModified = DateTime.MinValue;

                return HardwareOperationResult.CreateSuccess($"Disque restauré: {disk.Model}");
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur restauration disque: {ex.Message}", ex);
            }
        }
    }

    /// <summary>
    /// Modèle représentant un périphérique de stockage
    /// </summary>
    public class DiskDevice
    {
        public string DeviceType { get; set; } = string.Empty;  // Physical, Logical, SCSI, NVMe
        public string DeviceId { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
        public string SerialNumber { get; set; } = string.Empty;
        public string FriendlyName { get; set; } = string.Empty;
        public string InterfaceType { get; set; } = string.Empty;  // SATA, NVMe, USB, etc.
        public string ConnectionType { get; set; } = string.Empty;  // USB, SCSI/SATA, NVMe, IDE
        public string MediaType { get; set; } = string.Empty;
        public string FileSystem { get; set; } = string.Empty;
        public string VolumeLabel { get; set; } = string.Empty;
        public string VolumeSerialNumber { get; set; } = string.Empty;
        public string Signature { get; set; } = string.Empty;
        public string PnpDeviceId { get; set; } = string.Empty;
        public string RegistryPath { get; set; } = string.Empty;
        public long Size { get; set; }
        public long FreeSpace { get; set; }
        public int Partitions { get; set; }
        public List<string> HardwareIds { get; set; } = new();
        public bool IsModified { get; set; } = false;
        public DateTime LastModified { get; set; } = DateTime.MinValue;

        public string DisplayName => $"{Model} ({FormatSize(Size)}) - {ConnectionType}";
        public string SizeFormatted => FormatSize(Size);

        private string FormatSize(long bytes)
        {
            if (bytes == 0) return "0 B";
            
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            int order = 0;
            double size = bytes;
            
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }
            
            return $"{size:0.##} {sizes[order]}";
        }

        public override string ToString() => DisplayName;
    }
}
