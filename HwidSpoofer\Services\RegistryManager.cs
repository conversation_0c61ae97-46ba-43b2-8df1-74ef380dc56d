using System;
using System.Collections.Generic;
using Microsoft.Win32;
using HwidSpoofer.Models;

namespace HwidSpoofer.Services
{
    /// <summary>
    /// Service de gestion des modifications du registre Windows
    /// </summary>
    public class RegistryManager
    {
        private readonly Dictionary<string, object> _originalValues = new();

        /// <summary>
        /// Lit une valeur du registre
        /// </summary>
        public HardwareOperationResult GetRegistryValue(string registryPath, string keyName)
        {
            try
            {
                var (hive, subKey) = ParseRegistryPath(registryPath);
                
                using var key = hive.OpenSubKey(subKey, false);
                if (key == null)
                {
                    return HardwareOperationResult.CreateError($"Clé de registre non trouvée: {registryPath}");
                }

                var value = key.GetValue(keyName);
                if (value == null)
                {
                    return HardwareOperationResult.CreateError($"Valeur de registre non trouvée: {keyName}");
                }

                return HardwareOperationResult.CreateSuccess($"Valeur lue: {value}");
            }
            catch (UnauthorizedAccessException)
            {
                return HardwareOperationResult.CreateError("Accès refusé au registre. Privilèges administrateur requis.");
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur lors de la lecture du registre: {ex.Message}");
            }
        }

        /// <summary>
        /// Écrit une valeur dans le registre
        /// </summary>
        public HardwareOperationResult SetRegistryValue(string registryPath, string keyName, object value)
        {
            try
            {
                var (hive, subKey) = ParseRegistryPath(registryPath);
                var fullPath = $"{registryPath}\\{keyName}";

                // Sauvegarder la valeur originale si ce n'est pas déjà fait
                if (!_originalValues.ContainsKey(fullPath))
                {
                    var originalResult = GetRegistryValue(registryPath, keyName);
                    if (originalResult.Success)
                    {
                        using var readKey = hive.OpenSubKey(subKey, false);
                        if (readKey != null)
                        {
                            var originalValue = readKey.GetValue(keyName);
                            if (originalValue != null)
                            {
                                _originalValues[fullPath] = originalValue;
                            }
                        }
                    }
                }

                // Ouvrir la clé en écriture
                using var key = hive.OpenSubKey(subKey, true);
                if (key == null)
                {
                    return HardwareOperationResult.CreateError($"Impossible d'ouvrir la clé de registre en écriture: {registryPath}");
                }

                // Déterminer le type de valeur approprié
                RegistryValueKind valueKind = value switch
                {
                    string => RegistryValueKind.String,
                    int => RegistryValueKind.DWord,
                    long => RegistryValueKind.QWord,
                    byte[] => RegistryValueKind.Binary,
                    _ => RegistryValueKind.String
                };

                // Écrire la valeur
                key.SetValue(keyName, value, valueKind);

                return HardwareOperationResult.CreateSuccess($"Valeur de registre modifiée: {keyName} = {value}");
            }
            catch (UnauthorizedAccessException)
            {
                return HardwareOperationResult.CreateError("Accès refusé au registre. Privilèges administrateur requis.");
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur lors de l'écriture du registre: {ex.Message}");
            }
        }

        /// <summary>
        /// Restaure une valeur originale du registre
        /// </summary>
        public HardwareOperationResult RestoreRegistryValue(string registryPath, string keyName)
        {
            try
            {
                var fullPath = $"{registryPath}\\{keyName}";
                
                if (!_originalValues.ContainsKey(fullPath))
                {
                    return HardwareOperationResult.CreateError($"Aucune valeur originale sauvegardée pour {fullPath}");
                }

                var originalValue = _originalValues[fullPath];
                var result = SetRegistryValue(registryPath, keyName, originalValue);
                
                if (result.Success)
                {
                    _originalValues.Remove(fullPath);
                    return HardwareOperationResult.CreateSuccess($"Valeur de registre restaurée: {keyName}");
                }

                return result;
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur lors de la restauration du registre: {ex.Message}");
            }
        }

        /// <summary>
        /// Restaure toutes les valeurs originales sauvegardées
        /// </summary>
        public HardwareOperationResult RestoreAllOriginalValues()
        {
            try
            {
                var errors = new List<string>();
                var restored = 0;

                foreach (var kvp in _originalValues)
                {
                    var fullPath = kvp.Key;
                    var lastSlash = fullPath.LastIndexOf('\\');
                    if (lastSlash == -1) continue;

                    var registryPath = fullPath.Substring(0, lastSlash);
                    var keyName = fullPath.Substring(lastSlash + 1);

                    var result = SetRegistryValue(registryPath, keyName, kvp.Value);
                    if (result.Success)
                    {
                        restored++;
                    }
                    else
                    {
                        errors.Add($"{keyName}: {result.Message}");
                    }
                }

                _originalValues.Clear();

                if (errors.Count > 0)
                {
                    var errorMessage = $"Erreurs lors de la restauration:\n{string.Join("\n", errors)}";
                    return HardwareOperationResult.CreateError(errorMessage);
                }

                return HardwareOperationResult.CreateSuccess($"Restauration réussie de {restored} valeurs de registre");
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur lors de la restauration globale: {ex.Message}");
            }
        }

        /// <summary>
        /// Vérifie si une clé de registre existe
        /// </summary>
        public bool RegistryKeyExists(string registryPath)
        {
            try
            {
                var (hive, subKey) = ParseRegistryPath(registryPath);
                using var key = hive.OpenSubKey(subKey, false);
                return key != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Vérifie si une valeur de registre existe
        /// </summary>
        public bool RegistryValueExists(string registryPath, string keyName)
        {
            try
            {
                var (hive, subKey) = ParseRegistryPath(registryPath);
                using var key = hive.OpenSubKey(subKey, false);
                if (key == null) return false;

                var value = key.GetValue(keyName);
                return value != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Crée une sauvegarde de toutes les valeurs modifiées
        /// </summary>
        public Dictionary<string, object> GetModifiedValues()
        {
            return new Dictionary<string, object>(_originalValues);
        }

        /// <summary>
        /// Charge des valeurs sauvegardées
        /// </summary>
        public void LoadBackupValues(Dictionary<string, object> backupValues)
        {
            _originalValues.Clear();
            foreach (var kvp in backupValues)
            {
                _originalValues[kvp.Key] = kvp.Value;
            }
        }

        /// <summary>
        /// Parse un chemin de registre pour extraire la ruche et la sous-clé
        /// </summary>
        private (RegistryKey hive, string subKey) ParseRegistryPath(string registryPath)
        {
            if (registryPath.StartsWith("HKEY_LOCAL_MACHINE\\"))
            {
                return (Registry.LocalMachine, registryPath.Substring("HKEY_LOCAL_MACHINE\\".Length));
            }
            else if (registryPath.StartsWith("HKEY_CURRENT_USER\\"))
            {
                return (Registry.CurrentUser, registryPath.Substring("HKEY_CURRENT_USER\\".Length));
            }
            else if (registryPath.StartsWith("HKEY_CLASSES_ROOT\\"))
            {
                return (Registry.ClassesRoot, registryPath.Substring("HKEY_CLASSES_ROOT\\".Length));
            }
            else if (registryPath.StartsWith("HKEY_USERS\\"))
            {
                return (Registry.Users, registryPath.Substring("HKEY_USERS\\".Length));
            }
            else if (registryPath.StartsWith("HKEY_CURRENT_CONFIG\\"))
            {
                return (Registry.CurrentConfig, registryPath.Substring("HKEY_CURRENT_CONFIG\\".Length));
            }
            else
            {
                throw new ArgumentException($"Chemin de registre non supporté: {registryPath}");
            }
        }

        /// <summary>
        /// Nettoie les ressources
        /// </summary>
        public void Dispose()
        {
            _originalValues.Clear();
        }
    }
}
