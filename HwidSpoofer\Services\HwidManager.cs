using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using Microsoft.Win32;
using HwidSpoofer.Models;

namespace HwidSpoofer.Services
{
    /// <summary>
    /// Service principal de gestion des modifications HWID
    /// </summary>
    public class HwidManager
    {
        private readonly FixedHardwareDetector _detector;
        private readonly RegistryManager _registryManager;
        private readonly BackupManager _backupManager;
        private Dictionary<string, HardwareInfo> _currentHardware = new();

        public event EventHandler<string>? LogMessage;
        public event EventHandler<HardwareOperationResult>? OperationCompleted;

        public HwidManager()
        {
            _detector = new FixedHardwareDetector();
            _registryManager = new RegistryManager();
            _backupManager = new BackupManager();
        }

        /// <summary>
        /// Initialise le gestionnaire et détecte le hardware
        /// </summary>
        public HardwareOperationResult Initialize()
        {
            try
            {
                LogMessage?.Invoke(this, "Initialisation du gestionnaire HWID...");
                
                // Créer une sauvegarde automatique au démarrage
                var backupResult = _backupManager.CreateBackup("Sauvegarde automatique - Démarrage");
                if (!backupResult.Success)
                {
                    return HardwareOperationResult.CreateError($"Impossible de créer la sauvegarde: {backupResult.Message}");
                }

                // Détecter le hardware actuel
                _currentHardware = _detector.DetectAllHardware();
                LogMessage?.Invoke(this, $"Hardware détecté: {_currentHardware.Count} composants");

                return HardwareOperationResult.CreateSuccess("Gestionnaire HWID initialisé avec succès");
            }
            catch (Exception ex)
            {
                var error = $"Erreur lors de l'initialisation: {ex.Message}";
                LogMessage?.Invoke(this, error);
                return HardwareOperationResult.CreateError(error, ex);
            }
        }

        /// <summary>
        /// Obtient la liste actuelle du hardware détecté
        /// </summary>
        public Dictionary<string, HardwareInfo> GetCurrentHardware()
        {
            return new Dictionary<string, HardwareInfo>(_currentHardware);
        }

        /// <summary>
        /// Rafraîchit la détection du hardware
        /// </summary>
        public HardwareOperationResult RefreshHardware()
        {
            try
            {
                LogMessage?.Invoke(this, "Rafraîchissement de la détection hardware...");
                _currentHardware = _detector.DetectAllHardware();
                LogMessage?.Invoke(this, "Hardware rafraîchi avec succès");
                return HardwareOperationResult.CreateSuccess("Hardware rafraîchi");
            }
            catch (Exception ex)
            {
                var error = $"Erreur lors du rafraîchissement: {ex.Message}";
                LogMessage?.Invoke(this, error);
                return HardwareOperationResult.CreateError(error, ex);
            }
        }

        /// <summary>
        /// Modifie un identifiant hardware spécifique
        /// </summary>
        public HardwareOperationResult ModifyHardwareId(string hardwareKey, string newValue)
        {
            try
            {
                if (!_currentHardware.ContainsKey(hardwareKey))
                {
                    return HardwareOperationResult.CreateError($"Identifiant hardware '{hardwareKey}' non trouvé");
                }

                var hardware = _currentHardware[hardwareKey];
                
                if (!hardware.CanBeModified)
                {
                    return HardwareOperationResult.CreateError($"L'identifiant '{hardware.Name}' ne peut pas être modifié");
                }

                LogMessage?.Invoke(this, $"Modification de {hardware.Name}: {hardware.CurrentValue} -> {newValue}");

                // Sauvegarder la valeur originale si ce n'est pas déjà fait
                if (!hardware.IsModified)
                {
                    hardware.OriginalValue = hardware.CurrentValue;
                }

                // Appliquer la modification dans le registre si applicable
                if (!string.IsNullOrEmpty(hardware.RegistryPath) && !string.IsNullOrEmpty(hardware.RegistryKey))
                {
                    var registryResult = _registryManager.SetRegistryValue(
                        hardware.RegistryPath, 
                        hardware.RegistryKey, 
                        newValue
                    );

                    if (!registryResult.Success)
                    {
                        return HardwareOperationResult.CreateError($"Erreur registre: {registryResult.Message}");
                    }
                }

                // Mettre à jour l'objet hardware
                hardware.UpdateValue(newValue);
                _currentHardware[hardwareKey] = hardware;

                LogMessage?.Invoke(this, $"Modification appliquée avec succès pour {hardware.Name}");
                
                var result = HardwareOperationResult.CreateSuccess($"Identifiant '{hardware.Name}' modifié avec succès");
                OperationCompleted?.Invoke(this, result);
                
                return result;
            }
            catch (Exception ex)
            {
                var error = $"Erreur lors de la modification de {hardwareKey}: {ex.Message}";
                LogMessage?.Invoke(this, error);
                return HardwareOperationResult.CreateError(error, ex);
            }
        }

        /// <summary>
        /// Génère et applique une nouvelle valeur aléatoire pour un identifiant
        /// </summary>
        public HardwareOperationResult RandomizeHardwareId(string hardwareKey)
        {
            try
            {
                if (!_currentHardware.ContainsKey(hardwareKey))
                {
                    return HardwareOperationResult.CreateError($"Identifiant hardware '{hardwareKey}' non trouvé");
                }

                var newValue = _detector.GenerateRandomValue(hardwareKey);
                LogMessage?.Invoke(this, $"Génération d'une nouvelle valeur aléatoire pour {hardwareKey}: {newValue}");
                
                return ModifyHardwareId(hardwareKey, newValue);
            }
            catch (Exception ex)
            {
                var error = $"Erreur lors de la randomisation de {hardwareKey}: {ex.Message}";
                LogMessage?.Invoke(this, error);
                return HardwareOperationResult.CreateError(error, ex);
            }
        }

        /// <summary>
        /// Restaure un identifiant hardware à sa valeur originale
        /// </summary>
        public HardwareOperationResult RestoreHardwareId(string hardwareKey)
        {
            try
            {
                if (!_currentHardware.ContainsKey(hardwareKey))
                {
                    return HardwareOperationResult.CreateError($"Identifiant hardware '{hardwareKey}' non trouvé");
                }

                var hardware = _currentHardware[hardwareKey];
                
                if (!hardware.IsModified)
                {
                    return HardwareOperationResult.CreateSuccess($"L'identifiant '{hardware.Name}' n'a pas été modifié");
                }

                LogMessage?.Invoke(this, $"Restauration de {hardware.Name} à sa valeur originale: {hardware.OriginalValue}");

                // Restaurer dans le registre si applicable
                if (!string.IsNullOrEmpty(hardware.RegistryPath) && !string.IsNullOrEmpty(hardware.RegistryKey))
                {
                    var registryResult = _registryManager.SetRegistryValue(
                        hardware.RegistryPath, 
                        hardware.RegistryKey, 
                        hardware.OriginalValue
                    );

                    if (!registryResult.Success)
                    {
                        return HardwareOperationResult.CreateError($"Erreur registre lors de la restauration: {registryResult.Message}");
                    }
                }

                // Restaurer l'objet hardware
                hardware.RestoreOriginal();
                _currentHardware[hardwareKey] = hardware;

                LogMessage?.Invoke(this, $"Restauration réussie pour {hardware.Name}");
                
                var result = HardwareOperationResult.CreateSuccess($"Identifiant '{hardware.Name}' restauré avec succès");
                OperationCompleted?.Invoke(this, result);
                
                return result;
            }
            catch (Exception ex)
            {
                var error = $"Erreur lors de la restauration de {hardwareKey}: {ex.Message}";
                LogMessage?.Invoke(this, error);
                return HardwareOperationResult.CreateError(error, ex);
            }
        }

        /// <summary>
        /// Restaure tous les identifiants modifiés
        /// </summary>
        public HardwareOperationResult RestoreAllModified()
        {
            try
            {
                LogMessage?.Invoke(this, "Restauration de tous les identifiants modifiés...");
                
                var modifiedItems = new List<string>();
                var errors = new List<string>();

                foreach (var kvp in _currentHardware)
                {
                    if (kvp.Value.IsModified)
                    {
                        modifiedItems.Add(kvp.Key);
                        var result = RestoreHardwareId(kvp.Key);
                        if (!result.Success)
                        {
                            errors.Add($"{kvp.Value.Name}: {result.Message}");
                        }
                    }
                }

                if (errors.Count > 0)
                {
                    var errorMessage = $"Erreurs lors de la restauration:\n{string.Join("\n", errors)}";
                    return HardwareOperationResult.CreateError(errorMessage);
                }

                var successMessage = modifiedItems.Count > 0 
                    ? $"Restauration réussie de {modifiedItems.Count} identifiants"
                    : "Aucun identifiant modifié à restaurer";

                LogMessage?.Invoke(this, successMessage);
                return HardwareOperationResult.CreateSuccess(successMessage);
            }
            catch (Exception ex)
            {
                var error = $"Erreur lors de la restauration globale: {ex.Message}";
                LogMessage?.Invoke(this, error);
                return HardwareOperationResult.CreateError(error, ex);
            }
        }

        /// <summary>
        /// Crée une sauvegarde du hardware actuel
        /// </summary>
        public HardwareOperationResult CreateBackup(string description)
        {
            try
            {
                LogMessage?.Invoke(this, $"Création d'une sauvegarde: {description}");
                return _backupManager.CreateBackup(description);
            }
            catch (Exception ex)
            {
                var error = $"Erreur lors de la création de sauvegarde: {ex.Message}";
                LogMessage?.Invoke(this, error);
                return HardwareOperationResult.CreateError(error, ex);
            }
        }

        /// <summary>
        /// Obtient la liste des sauvegardes disponibles
        /// </summary>
        public List<HardwareBackup> GetAvailableBackups()
        {
            return _backupManager.GetAvailableBackups();
        }

        /// <summary>
        /// Restaure une sauvegarde spécifique
        /// </summary>
        public HardwareOperationResult RestoreBackup(DateTime backupDate)
        {
            try
            {
                LogMessage?.Invoke(this, $"Restauration de la sauvegarde du {backupDate}");
                var result = _backupManager.RestoreBackup(backupDate);
                
                if (result.Success)
                {
                    // Rafraîchir le hardware après restauration
                    RefreshHardware();
                }
                
                return result;
            }
            catch (Exception ex)
            {
                var error = $"Erreur lors de la restauration de sauvegarde: {ex.Message}";
                LogMessage?.Invoke(this, error);
                return HardwareOperationResult.CreateError(error, ex);
            }
        }

        /// <summary>
        /// Génère un rapport complet du hardware actuel
        /// </summary>
        public string GenerateHardwareReport()
        {
            try
            {
                var report = new System.Text.StringBuilder();
                report.AppendLine("=== RAPPORT HARDWARE HWID SPOOFER ===");
                report.AppendLine($"Généré le: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                report.AppendLine($"Empreinte Hardware: {_detector.GenerateHardwareFingerprint()}");
                report.AppendLine();

                var categories = new Dictionary<string, List<HardwareInfo>>();
                
                foreach (var hardware in _currentHardware.Values)
                {
                    if (!categories.ContainsKey(hardware.Category))
                    {
                        categories[hardware.Category] = new List<HardwareInfo>();
                    }
                    categories[hardware.Category].Add(hardware);
                }

                foreach (var category in categories)
                {
                    report.AppendLine($"=== {category.Key.ToUpper()} ===");
                    foreach (var item in category.Value)
                    {
                        report.AppendLine($"  {item.Name}: {item.CurrentValue}");
                        if (item.IsModified)
                        {
                            report.AppendLine($"    (Original: {item.OriginalValue})");
                            report.AppendLine($"    (Modifié le: {item.LastModified:yyyy-MM-dd HH:mm:ss})");
                        }
                    }
                    report.AppendLine();
                }

                return report.ToString();
            }
            catch (Exception ex)
            {
                return $"Erreur lors de la génération du rapport: {ex.Message}";
            }
        }
    }
}
