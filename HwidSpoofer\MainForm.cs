using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using HwidSpoofer.Models;
using HwidSpoofer.Services;

namespace HwidSpoofer
{
    public partial class MainForm : Form
    {
        private readonly HwidManager _hwidManager;
        private ListView _hardwareListView = null!;
        private TextBox _logTextBox = null!;
        private Button _refreshButton = null!;
        private Button _randomizeButton = null!;
        private Button _restoreButton = null!;
        private Button _restoreAllButton = null!;
        private Button _backupButton = null!;
        private Button _restoreBackupButton = null!;
        private Button _generateReportButton = null!;
        private StatusStrip _statusStrip = null!;
        private ToolStripStatusLabel _statusLabel = null!;
        private TabControl _tabControl = null!;

        public MainForm()
        {
            _hwidManager = new HwidManager();
            InitializeComponent();
            SetupEventHandlers();
            InitializeManager();
        }

        private void InitializeComponent()
        {
            // Configuration de la fenêtre principale
            Text = "HWID Spoofer - Outil Éducatif v1.0";
            Size = new Size(1000, 700);
            StartPosition = FormStartPosition.CenterScreen;
            MinimumSize = new Size(800, 600);
            Icon = SystemIcons.Shield;

            // Création du TabControl principal
            _tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 9F)
            };

            // Onglet Hardware
            var hardwareTab = new TabPage("Hardware Détecté");
            CreateHardwareTab(hardwareTab);
            _tabControl.TabPages.Add(hardwareTab);

            // Onglet Logs
            var logsTab = new TabPage("Logs & Activité");
            CreateLogsTab(logsTab);
            _tabControl.TabPages.Add(logsTab);

            // Onglet Sauvegardes
            var backupsTab = new TabPage("Sauvegardes");
            CreateBackupsTab(backupsTab);
            _tabControl.TabPages.Add(backupsTab);

            // Barre de statut
            _statusStrip = new StatusStrip();
            _statusLabel = new ToolStripStatusLabel("Prêt")
            {
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };
            _statusStrip.Items.Add(_statusLabel);

            // Ajout des contrôles à la fenêtre
            Controls.Add(_tabControl);
            Controls.Add(_statusStrip);

            // Avertissement en haut
            var warningPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(255, 255, 200),
                BorderStyle = BorderStyle.FixedSingle
            };

            var warningLabel = new Label
            {
                Text = "⚠️ ATTENTION: Cet outil est destiné uniquement à des fins éducatives. " +
                       "L'utilisation malveillante est interdite et peut être illégale.",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.DarkRed
            };

            warningPanel.Controls.Add(warningLabel);
            Controls.Add(warningPanel);
            warningPanel.BringToFront();
        }

        private void CreateHardwareTab(TabPage tab)
        {
            var mainPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // Panel des boutons
            var buttonPanel = new Panel { Height = 50, Dock = DockStyle.Top };
            
            _refreshButton = new Button
            {
                Text = "🔄 Rafraîchir",
                Size = new Size(100, 30),
                Location = new Point(0, 10)
            };

            _randomizeButton = new Button
            {
                Text = "🎲 Randomiser",
                Size = new Size(100, 30),
                Location = new Point(110, 10),
                Enabled = false
            };

            _restoreButton = new Button
            {
                Text = "↶ Restaurer",
                Size = new Size(100, 30),
                Location = new Point(220, 10),
                Enabled = false
            };

            _restoreAllButton = new Button
            {
                Text = "↶ Tout Restaurer",
                Size = new Size(120, 30),
                Location = new Point(330, 10)
            };

            _generateReportButton = new Button
            {
                Text = "📄 Rapport",
                Size = new Size(100, 30),
                Location = new Point(460, 10)
            };

            buttonPanel.Controls.AddRange(new Control[] 
            { 
                _refreshButton, _randomizeButton, _restoreButton, 
                _restoreAllButton, _generateReportButton 
            });

            // ListView pour le hardware
            _hardwareListView = new ListView
            {
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                MultiSelect = false,
                Font = new Font("Consolas", 9F)
            };

            _hardwareListView.Columns.Add("Composant", 200);
            _hardwareListView.Columns.Add("Catégorie", 100);
            _hardwareListView.Columns.Add("Valeur Actuelle", 300);
            _hardwareListView.Columns.Add("Valeur Originale", 300);
            _hardwareListView.Columns.Add("État", 80);

            mainPanel.Controls.Add(_hardwareListView);
            mainPanel.Controls.Add(buttonPanel);
            tab.Controls.Add(mainPanel);
        }

        private void CreateLogsTab(TabPage tab)
        {
            var mainPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            var clearButton = new Button
            {
                Text = "🗑️ Effacer Logs",
                Size = new Size(120, 30),
                Dock = DockStyle.Top
            };

            _logTextBox = new TextBox
            {
                Dock = DockStyle.Fill,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Font = new Font("Consolas", 9F),
                BackColor = Color.Black,
                ForeColor = Color.LimeGreen
            };

            clearButton.Click += (s, e) => _logTextBox.Clear();

            mainPanel.Controls.Add(_logTextBox);
            mainPanel.Controls.Add(clearButton);
            tab.Controls.Add(mainPanel);
        }

        private void CreateBackupsTab(TabPage tab)
        {
            var mainPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            var buttonPanel = new Panel { Height = 50, Dock = DockStyle.Top };

            _backupButton = new Button
            {
                Text = "💾 Créer Sauvegarde",
                Size = new Size(140, 30),
                Location = new Point(0, 10)
            };

            _restoreBackupButton = new Button
            {
                Text = "📂 Restaurer Sauvegarde",
                Size = new Size(160, 30),
                Location = new Point(150, 10)
            };

            buttonPanel.Controls.AddRange(new Control[] { _backupButton, _restoreBackupButton });

            var backupListView = new ListView
            {
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true
            };

            backupListView.Columns.Add("Date", 150);
            backupListView.Columns.Add("Description", 300);
            backupListView.Columns.Add("Éléments", 100);

            mainPanel.Controls.Add(backupListView);
            mainPanel.Controls.Add(buttonPanel);
            tab.Controls.Add(mainPanel);
        }

        private void SetupEventHandlers()
        {
            _hwidManager.LogMessage += OnLogMessage;
            _hwidManager.OperationCompleted += OnOperationCompleted;

            _refreshButton.Click += OnRefreshClick;
            _randomizeButton.Click += OnRandomizeClick;
            _restoreButton.Click += OnRestoreClick;
            _restoreAllButton.Click += OnRestoreAllClick;
            _backupButton.Click += OnBackupClick;
            _restoreBackupButton.Click += OnRestoreBackupClick;
            _generateReportButton.Click += OnGenerateReportClick;

            _hardwareListView.SelectedIndexChanged += OnHardwareSelectionChanged;

            FormClosing += OnFormClosing;
        }

        private void InitializeManager()
        {
            try
            {
                var result = _hwidManager.Initialize();
                if (result.Success)
                {
                    RefreshHardwareList();
                    _statusLabel.Text = "Gestionnaire HWID initialisé avec succès";
                }
                else
                {
                    MessageBox.Show(
                        $"Erreur lors de l'initialisation:\n{result.Message}",
                        "Erreur",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error
                    );
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Erreur critique lors de l'initialisation:\n{ex.Message}",
                    "Erreur Critique",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        private void RefreshHardwareList()
        {
            try
            {
                _hardwareListView.Items.Clear();
                var hardware = _hwidManager.GetCurrentHardware();

                foreach (var kvp in hardware.OrderBy(h => h.Value.Category).ThenBy(h => h.Value.Name))
                {
                    var item = kvp.Value;
                    var listItem = new ListViewItem(item.Name)
                    {
                        Tag = kvp.Key
                    };

                    listItem.SubItems.Add(item.Category);
                    listItem.SubItems.Add(item.CurrentValue);
                    listItem.SubItems.Add(item.OriginalValue);
                    listItem.SubItems.Add(item.IsModified ? "Modifié" : "Original");

                    // Coloration selon l'état
                    if (!item.CanBeModified)
                    {
                        listItem.ForeColor = Color.Gray;
                    }
                    else if (item.IsModified)
                    {
                        listItem.BackColor = Color.LightYellow;
                        listItem.ForeColor = Color.DarkBlue;
                    }

                    _hardwareListView.Items.Add(listItem);
                }

                _statusLabel.Text = $"Hardware rafraîchi - {hardware.Count} composants détectés";
            }
            catch (Exception ex)
            {
                LogMessage($"Erreur lors du rafraîchissement: {ex.Message}");
            }
        }

        private void OnLogMessage(object? sender, string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<object?, string>(OnLogMessage), sender, message);
                return;
            }

            LogMessage(message);
        }

        private void LogMessage(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            _logTextBox.AppendText($"[{timestamp}] {message}\r\n");
            _logTextBox.ScrollToCaret();
        }

        private void OnOperationCompleted(object? sender, HardwareOperationResult result)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<object?, HardwareOperationResult>(OnOperationCompleted), sender, result);
                return;
            }

            _statusLabel.Text = result.Message;
            
            if (!result.Success)
            {
                MessageBox.Show(
                    result.Message,
                    "Erreur d'Opération",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );
            }

            RefreshHardwareList();
        }

        private void OnRefreshClick(object? sender, EventArgs e)
        {
            var result = _hwidManager.RefreshHardware();
            if (result.Success)
            {
                RefreshHardwareList();
            }
        }

        private void OnRandomizeClick(object? sender, EventArgs e)
        {
            if (_hardwareListView.SelectedItems.Count == 0) return;

            var selectedKey = _hardwareListView.SelectedItems[0].Tag?.ToString();
            if (string.IsNullOrEmpty(selectedKey)) return;

            var confirmResult = MessageBox.Show(
                $"Êtes-vous sûr de vouloir randomiser cet identifiant ?\n\n" +
                "Cette action modifiera définitivement la valeur jusqu'à restauration.",
                "Confirmation de Randomisation",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question
            );

            if (confirmResult == DialogResult.Yes)
            {
                _hwidManager.RandomizeHardwareId(selectedKey);
            }
        }

        private void OnRestoreClick(object? sender, EventArgs e)
        {
            if (_hardwareListView.SelectedItems.Count == 0) return;

            var selectedKey = _hardwareListView.SelectedItems[0].Tag?.ToString();
            if (string.IsNullOrEmpty(selectedKey)) return;

            _hwidManager.RestoreHardwareId(selectedKey);
        }

        private void OnRestoreAllClick(object? sender, EventArgs e)
        {
            var confirmResult = MessageBox.Show(
                "Êtes-vous sûr de vouloir restaurer TOUS les identifiants modifiés ?\n\n" +
                "Cette action restaurera toutes les valeurs originales.",
                "Confirmation de Restauration Globale",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question
            );

            if (confirmResult == DialogResult.Yes)
            {
                _hwidManager.RestoreAllModified();
            }
        }

        private void OnBackupClick(object? sender, EventArgs e)
        {
            var description = Microsoft.VisualBasic.Interaction.InputBox(
                "Entrez une description pour cette sauvegarde:",
                "Créer une Sauvegarde",
                $"Sauvegarde manuelle - {DateTime.Now:yyyy-MM-dd HH:mm}"
            );

            if (!string.IsNullOrEmpty(description))
            {
                _hwidManager.CreateBackup(description);
            }
        }

        private void OnRestoreBackupClick(object? sender, EventArgs e)
        {
            var backups = _hwidManager.GetAvailableBackups();
            if (backups.Count == 0)
            {
                MessageBox.Show(
                    "Aucune sauvegarde disponible.",
                    "Pas de Sauvegarde",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
                return;
            }

            // Ici, vous pourriez implémenter une fenêtre de sélection de sauvegarde
            // Pour simplifier, on prend la plus récente
            var latestBackup = backups.OrderByDescending(b => b.CreatedAt).First();
            
            var confirmResult = MessageBox.Show(
                $"Restaurer la sauvegarde du {latestBackup.CreatedAt:yyyy-MM-dd HH:mm} ?\n\n" +
                $"Description: {latestBackup.Description}",
                "Confirmer Restauration",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question
            );

            if (confirmResult == DialogResult.Yes)
            {
                _hwidManager.RestoreBackup(latestBackup.CreatedAt);
            }
        }

        private void OnGenerateReportClick(object? sender, EventArgs e)
        {
            try
            {
                var report = _hwidManager.GenerateHardwareReport();
                var reportForm = new Form
                {
                    Text = "Rapport Hardware",
                    Size = new Size(800, 600),
                    StartPosition = FormStartPosition.CenterParent
                };

                var textBox = new TextBox
                {
                    Dock = DockStyle.Fill,
                    Multiline = true,
                    ScrollBars = ScrollBars.Both,
                    ReadOnly = true,
                    Font = new Font("Consolas", 9F),
                    Text = report
                };

                reportForm.Controls.Add(textBox);
                reportForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Erreur lors de la génération du rapport:\n{ex.Message}",
                    "Erreur",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        private void OnHardwareSelectionChanged(object? sender, EventArgs e)
        {
            var hasSelection = _hardwareListView.SelectedItems.Count > 0;
            _randomizeButton.Enabled = hasSelection;
            _restoreButton.Enabled = hasSelection;

            if (hasSelection)
            {
                var selectedKey = _hardwareListView.SelectedItems[0].Tag?.ToString();
                if (!string.IsNullOrEmpty(selectedKey))
                {
                    var hardware = _hwidManager.GetCurrentHardware();
                    if (hardware.ContainsKey(selectedKey))
                    {
                        var item = hardware[selectedKey];
                        _randomizeButton.Enabled = item.CanBeModified;
                        _restoreButton.Enabled = item.IsModified;
                    }
                }
            }
        }

        private void OnFormClosing(object? sender, FormClosingEventArgs e)
        {
            var modifiedItems = _hwidManager.GetCurrentHardware().Values.Count(h => h.IsModified);
            
            if (modifiedItems > 0)
            {
                var result = MessageBox.Show(
                    $"Vous avez {modifiedItems} identifiant(s) modifié(s).\n\n" +
                    "Voulez-vous les restaurer avant de quitter ?\n\n" +
                    "Oui = Restaurer et quitter\n" +
                    "Non = Quitter sans restaurer\n" +
                    "Annuler = Ne pas quitter",
                    "Identifiants Modifiés",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Warning
                );

                switch (result)
                {
                    case DialogResult.Yes:
                        _hwidManager.RestoreAllModified();
                        break;
                    case DialogResult.Cancel:
                        e.Cancel = true;
                        return;
                }
            }
        }
    }
}
