@echo off
echo ========================================
echo    HWID Spoofer - Outil Educatif
echo ========================================
echo.
echo ATTENTION: Cet outil est destine uniquement
echo a des fins educatives et de recherche.
echo.
echo L'utilisation malveillante est interdite
echo et peut etre illegale.
echo.
echo ========================================
echo.

REM Vérifier si on est déjà administrateur
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Privileges administrateur detectes.
    echo Lancement de l'application...
    echo.
    goto :run_app
) else (
    echo Privileges administrateur requis.
    echo Tentative de relancement en tant qu'administrateur...
    echo.
    
    REM Relancer en tant qu'administrateur
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

:run_app
REM Aller dans le répertoire de l'application
cd /d "%~dp0"

REM Vérifier si l'exécutable existe
if exist "HwidSpoofer\bin\Release\net6.0-windows\HwidSpoofer.exe" (
    echo Lancement de la version Release...
    "HwidSpoofer\bin\Release\net6.0-windows\HwidSpoofer.exe"
) else if exist "HwidSpoofer\bin\Debug\net6.0-windows\HwidSpoofer.exe" (
    echo Lancement de la version Debug...
    "HwidSpoofer\bin\Debug\net6.0-windows\HwidSpoofer.exe"
) else (
    echo ERREUR: Executable non trouve.
    echo.
    echo Veuillez compiler le projet d'abord:
    echo   dotnet build HwidSpoofer.sln --configuration Release
    echo.
    pause
    exit /b 1
)

echo.
echo Application fermee.
pause
