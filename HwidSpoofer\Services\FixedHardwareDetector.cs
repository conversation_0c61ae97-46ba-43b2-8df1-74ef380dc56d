using System;
using System.Collections.Generic;
using System.Management;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Win32;
using HwidSpoofer.Models;

namespace HwidSpoofer.Services
{
    /// <summary>
    /// Version CORRIGÉE du détecteur hardware avec les vrais chemins modifiables
    /// </summary>
    public class FixedHardwareDetector
    {
        private readonly Dictionary<string, HardwareInfo> _detectedHardware = new();

        /// <summary>
        /// Détecte toutes les informations hardware avec les VRAIS chemins modifiables
        /// </summary>
        public Dictionary<string, HardwareInfo> DetectAllHardware()
        {
            _detectedHardware.Clear();

            try
            {
                DetectModifiableSystemInfo();
                DetectModifiableNetworkInfo();
                DetectModifiableStorageInfo();
                DetectModifiableRegistryValues();
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de la détection hardware: {ex.Message}", ex);
            }

            return new Dictionary<string, HardwareInfo>(_detectedHardware);
        }

        /// <summary>
        /// Détecte les informations système RÉELLEMENT modifiables
        /// </summary>
        private void DetectModifiableSystemInfo()
        {
            try
            {
                // 1. MACHINE GUID - LE SEUL VRAIMENT MODIFIABLE FACILEMENT
                using var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Cryptography");
                var machineGuid = key?.GetValue("MachineGuid")?.ToString() ?? "N/A";

                _detectedHardware["MACHINE_GUID"] = new HardwareInfo(
                    "GUID Machine (Modifiable)",
                    "System",
                    machineGuid,
                    @"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography",
                    "MachineGuid"
                );

                // 2. INSTALLATION ID (aussi modifiable)
                try
                {
                    using var installKey = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion");
                    var installationId = installKey?.GetValue("InstallDate")?.ToString() ?? "N/A";
                    var productId = installKey?.GetValue("ProductId")?.ToString() ?? "N/A";

                    _detectedHardware["PRODUCT_ID"] = new HardwareInfo(
                        "Product ID Windows",
                        "System",
                        productId,
                        @"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion",
                        "ProductId"
                    );
                }
                catch (Exception ex)
                {
                    AddErrorInfo("PRODUCT_ID_ERROR", "Erreur Product ID", ex.Message);
                }

                // 3. COMPUTER NAME (modifiable)
                try
                {
                    using var computerKey = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName");
                    var computerName = computerKey?.GetValue("ComputerName")?.ToString() ?? Environment.MachineName;

                    _detectedHardware["COMPUTER_NAME"] = new HardwareInfo(
                        "Nom de l'Ordinateur",
                        "System",
                        computerName,
                        @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName",
                        "ComputerName"
                    );
                }
                catch (Exception ex)
                {
                    AddErrorInfo("COMPUTER_NAME_ERROR", "Erreur Nom Ordinateur", ex.Message);
                }
            }
            catch (Exception ex)
            {
                AddErrorInfo("SYSTEM_ERROR", "Erreur Système", ex.Message);
            }
        }

        /// <summary>
        /// Détecte les informations réseau modifiables
        /// </summary>
        private void DetectModifiableNetworkInfo()
        {
            try
            {
                // Chercher les vraies clés de cartes réseau dans le registre
                using var networkKey = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}");
                if (networkKey == null) return;

                int adapterIndex = 0;
                foreach (string subKeyName in networkKey.GetSubKeyNames())
                {
                    // Ignorer les clés non numériques
                    if (!int.TryParse(subKeyName, out _)) continue;

                    try
                    {
                        using var adapterKey = networkKey.OpenSubKey(subKeyName);
                        if (adapterKey == null) continue;

                        var driverDesc = adapterKey.GetValue("DriverDesc")?.ToString();
                        var networkAddress = adapterKey.GetValue("NetworkAddress")?.ToString();
                        
                        // Vérifier si c'est un vrai adaptateur physique
                        if (!string.IsNullOrEmpty(driverDesc) && !driverDesc.Contains("Virtual") && !driverDesc.Contains("Loopback"))
                        {
                            // Obtenir la MAC depuis WMI pour comparaison
                            var realMac = GetRealMacAddress(driverDesc);
                            
                            _detectedHardware[$"NETWORK_MAC_{adapterIndex}"] = new HardwareInfo(
                                $"Adresse MAC {adapterIndex} ({driverDesc})",
                                "Network",
                                realMac ?? "N/A",
                                $@"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{{4d36e972-e325-11ce-bfc1-08002be10318}}\{subKeyName}",
                                "NetworkAddress"
                            );

                            adapterIndex++;
                            if (adapterIndex >= 3) break; // Limiter à 3 adaptateurs
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Erreur adaptateur {subKeyName}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                AddErrorInfo("NETWORK_ERROR", "Erreur Réseau", ex.Message);
            }
        }

        /// <summary>
        /// Détecte les informations de stockage modifiables
        /// </summary>
        private void DetectModifiableStorageInfo()
        {
            try
            {
                // Chercher dans les clés de périphériques de stockage
                var storagePaths = new[]
                {
                    @"SYSTEM\CurrentControlSet\Enum\USBSTOR",
                    @"SYSTEM\CurrentControlSet\Enum\SCSI",
                    @"SYSTEM\CurrentControlSet\Enum\IDE"
                };

                int diskIndex = 0;
                foreach (var basePath in storagePaths)
                {
                    try
                    {
                        using var storageKey = Registry.LocalMachine.OpenSubKey(basePath);
                        if (storageKey == null) continue;

                        foreach (string deviceName in storageKey.GetSubKeyNames())
                        {
                            using var deviceKey = storageKey.OpenSubKey(deviceName);
                            if (deviceKey == null) continue;

                            foreach (string instanceName in deviceKey.GetSubKeyNames())
                            {
                                using var instanceKey = deviceKey.OpenSubKey(instanceName);
                                if (instanceKey == null) continue;

                                var friendlyName = instanceKey.GetValue("FriendlyName")?.ToString();
                                var hardwareIds = instanceKey.GetValue("HardwareID") as string[];

                                if (!string.IsNullOrEmpty(friendlyName))
                                {
                                    _detectedHardware[$"STORAGE_DEVICE_{diskIndex}"] = new HardwareInfo(
                                        $"Périphérique Stockage {diskIndex} ({friendlyName})",
                                        "Storage",
                                        friendlyName,
                                        $@"HKEY_LOCAL_MACHINE\{basePath}\{deviceName}\{instanceName}",
                                        "FriendlyName"
                                    );

                                    diskIndex++;
                                    if (diskIndex >= 5) return; // Limiter à 5 périphériques
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Erreur stockage {basePath}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                AddErrorInfo("STORAGE_ERROR", "Erreur Stockage", ex.Message);
            }
        }

        /// <summary>
        /// Détecte d'autres valeurs modifiables dans le registre
        /// </summary>
        private void DetectModifiableRegistryValues()
        {
            try
            {
                // 1. Informations utilisateur modifiables
                using var userKey = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion");
                if (userKey != null)
                {
                    var registeredOwner = userKey.GetValue("RegisteredOwner")?.ToString() ?? "N/A";
                    var registeredOrganization = userKey.GetValue("RegisteredOrganization")?.ToString() ?? "N/A";

                    _detectedHardware["REGISTERED_OWNER"] = new HardwareInfo(
                        "Propriétaire Enregistré",
                        "System",
                        registeredOwner,
                        @"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion",
                        "RegisteredOwner"
                    );

                    _detectedHardware["REGISTERED_ORG"] = new HardwareInfo(
                        "Organisation Enregistrée",
                        "System",
                        registeredOrganization,
                        @"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion",
                        "RegisteredOrganization"
                    );
                }

                // 2. Timezone (modifiable)
                using var timezoneKey = Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Control\TimeZoneInformation");
                if (timezoneKey != null)
                {
                    var timezoneName = timezoneKey.GetValue("TimeZoneKeyName")?.ToString() ?? "N/A";

                    _detectedHardware["TIMEZONE"] = new HardwareInfo(
                        "Fuseau Horaire",
                        "System",
                        timezoneName,
                        @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\TimeZoneInformation",
                        "TimeZoneKeyName"
                    );
                }
            }
            catch (Exception ex)
            {
                AddErrorInfo("REGISTRY_ERROR", "Erreur Registre", ex.Message);
            }
        }

        /// <summary>
        /// Obtient la vraie adresse MAC d'un adaptateur
        /// </summary>
        private string? GetRealMacAddress(string adapterDescription)
        {
            try
            {
                using var searcher = new ManagementObjectSearcher(
                    $"SELECT MACAddress FROM Win32_NetworkAdapter WHERE Description = '{adapterDescription.Replace("'", "''")}'");
                
                foreach (ManagementObject adapter in searcher.Get())
                {
                    return adapter["MACAddress"]?.ToString();
                }
            }
            catch
            {
                // Ignorer les erreurs WMI
            }
            return null;
        }

        /// <summary>
        /// Ajoute une information d'erreur
        /// </summary>
        private void AddErrorInfo(string key, string name, string error)
        {
            _detectedHardware[key] = new HardwareInfo(
                name,
                "Error",
                $"Erreur: {error}",
                "",
                ""
            )
            {
                CanBeModified = false
            };
        }

        /// <summary>
        /// Génère une nouvelle valeur aléatoire pour un type d'identifiant donné
        /// </summary>
        public string GenerateRandomValue(string hardwareType)
        {
            var random = new Random();
            
            return hardwareType.ToUpper() switch
            {
                var key when key.Contains("GUID") => Guid.NewGuid().ToString().ToUpper(),
                var key when key.Contains("MAC") => GenerateRandomMacAddress(),
                var key when key.Contains("NAME") => $"Computer-{GenerateRandomAlphaNumeric(6)}",
                var key when key.Contains("OWNER") => $"User-{GenerateRandomAlphaNumeric(8)}",
                var key when key.Contains("ORG") => $"Organization-{GenerateRandomAlphaNumeric(6)}",
                var key when key.Contains("PRODUCT") => $"{random.Next(10000, 99999)}-{random.Next(10000, 99999)}-{random.Next(10000, 99999)}-{random.Next(10000, 99999)}",
                _ => GenerateRandomAlphaNumeric(8)
            };
        }

        private string GenerateRandomMacAddress()
        {
            var random = new Random();
            var mac = new byte[6];
            random.NextBytes(mac);
            
            // S'assurer que c'est une adresse MAC valide (bit local admin à 0)
            mac[0] = (byte)(mac[0] & 0xFE);
            
            return string.Join(":", mac.Select(b => b.ToString("X2")));
        }

        private string GenerateRandomAlphaNumeric(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var random = new Random();
            var result = new StringBuilder();
            for (int i = 0; i < length; i++)
            {
                result.Append(chars[random.Next(chars.Length)]);
            }
            return result.ToString();
        }
    }
}
