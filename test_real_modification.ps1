# Test de modification RÉELLE du registre
# ATTENTION: Utilisation éducative uniquement

Write-Host "========================================" -ForegroundColor Yellow
Write-Host "    Test Modification HWID RÉELLE" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
Write-Host ""

# Vérifier si on est administrateur
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "ERREUR: Ce script doit être exécuté en tant qu'administrateur." -ForegroundColor Red
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

Write-Host "✓ Privilèges administrateur détectés" -ForegroundColor Green
Write-Host ""

# Fonction pour modifier une valeur de registre
function Set-RegistryValue {
    param(
        [string]$Path,
        [string]$Name,
        [string]$Value,
        [string]$Type = "String"
    )
    
    try {
        # Sauvegarder la valeur originale
        $originalValue = Get-ItemProperty -Path $Path -Name $Name -ErrorAction SilentlyContinue
        if ($originalValue) {
            Write-Host "   Valeur originale: $($originalValue.$Name)" -ForegroundColor Cyan
        }
        
        # Modifier la valeur
        Set-ItemProperty -Path $Path -Name $Name -Value $Value -Type $Type
        Write-Host "   ✓ Nouvelle valeur: $Value" -ForegroundColor Green
        
        # Vérifier la modification
        $newValue = Get-ItemProperty -Path $Path -Name $Name -ErrorAction SilentlyContinue
        if ($newValue -and $newValue.$Name -eq $Value) {
            Write-Host "   ✓ Modification confirmée dans le registre" -ForegroundColor Green
            return $true
        } else {
            Write-Host "   ✗ Échec de la modification" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "   ✗ Erreur: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test 1: Machine GUID (le plus facile à modifier)
Write-Host "=== TEST 1: MACHINE GUID ===" -ForegroundColor Cyan
$machineGuidPath = "HKLM:\SOFTWARE\Microsoft\Cryptography"
$newGuid = [System.Guid]::NewGuid().ToString().ToUpper()

Write-Host "Modification du Machine GUID..." -ForegroundColor White
$result1 = Set-RegistryValue -Path $machineGuidPath -Name "MachineGuid" -Value $newGuid
Write-Host ""

# Test 2: Nom de l'ordinateur
Write-Host "=== TEST 2: NOM ORDINATEUR ===" -ForegroundColor Cyan
$computerNamePath = "HKLM:\SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName"
$newComputerName = "DESKTOP-" + (Get-Random -Minimum 100000 -Maximum 999999)

Write-Host "Modification du nom d'ordinateur..." -ForegroundColor White
$result2 = Set-RegistryValue -Path $computerNamePath -Name "ComputerName" -Value $newComputerName
Write-Host ""

# Test 3: Propriétaire enregistré
Write-Host "=== TEST 3: PROPRIÉTAIRE ENREGISTRÉ ===" -ForegroundColor Cyan
$ownerPath = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion"
$newOwner = "TestUser" + (Get-Random -Minimum 1000 -Maximum 9999)

Write-Host "Modification du propriétaire enregistré..." -ForegroundColor White
$result3 = Set-RegistryValue -Path $ownerPath -Name "RegisteredOwner" -Value $newOwner
Write-Host ""

# Test 4: Adresse MAC (plus complexe)
Write-Host "=== TEST 4: ADRESSE MAC ===" -ForegroundColor Cyan
Write-Host "Recherche des adaptateurs réseau modifiables..." -ForegroundColor White

try {
    $networkPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}"
    $networkKey = Get-ChildItem -Path $networkPath -ErrorAction SilentlyContinue
    
    $found = $false
    foreach ($adapter in $networkKey) {
        $adapterPath = $adapter.PSPath
        $driverDesc = Get-ItemProperty -Path $adapterPath -Name "DriverDesc" -ErrorAction SilentlyContinue
        
        if ($driverDesc -and $driverDesc.DriverDesc -notlike "*Virtual*" -and $driverDesc.DriverDesc -notlike "*Loopback*") {
            Write-Host "   Adaptateur trouvé: $($driverDesc.DriverDesc)" -ForegroundColor Yellow
            
            # Générer une nouvelle MAC
            $newMac = ""
            for ($i = 0; $i -lt 6; $i++) {
                if ($i -eq 0) {
                    # Premier octet doit être pair pour une adresse unicast
                    $newMac += "{0:X2}" -f ((Get-Random -Minimum 0 -Maximum 255) -band 0xFE)
                } else {
                    $newMac += "{0:X2}" -f (Get-Random -Minimum 0 -Maximum 255)
                }
            }
            
            Write-Host "   Nouvelle MAC: $newMac" -ForegroundColor Cyan
            $result4 = Set-RegistryValue -Path $adapterPath -Name "NetworkAddress" -Value $newMac
            $found = $true
            break
        }
    }
    
    if (-not $found) {
        Write-Host "   Aucun adaptateur modifiable trouvé" -ForegroundColor Orange
        $result4 = $false
    }
}
catch {
    Write-Host "   ✗ Erreur lors de la modification MAC: $($_.Exception.Message)" -ForegroundColor Red
    $result4 = $false
}
Write-Host ""

# Résumé des résultats
Write-Host "=== RÉSUMÉ DES TESTS ===" -ForegroundColor Cyan
Write-Host "Machine GUID:        $(if($result1){'✓ SUCCÈS'}else{'✗ ÉCHEC'})" -ForegroundColor $(if($result1){'Green'}else{'Red'})
Write-Host "Nom Ordinateur:      $(if($result2){'✓ SUCCÈS'}else{'✗ ÉCHEC'})" -ForegroundColor $(if($result2){'Green'}else{'Red'})
Write-Host "Propriétaire:        $(if($result3){'✓ SUCCÈS'}else{'✗ ÉCHEC'})" -ForegroundColor $(if($result3){'Green'}else{'Red'})
Write-Host "Adresse MAC:         $(if($result4){'✓ SUCCÈS'}else{'✗ ÉCHEC'})" -ForegroundColor $(if($result4){'Green'}else{'Red'})
Write-Host ""

$successCount = @($result1, $result2, $result3, $result4) | Where-Object { $_ } | Measure-Object | Select-Object -ExpandProperty Count

if ($successCount -gt 0) {
    Write-Host "✓ $successCount modification(s) réussie(s) sur 4" -ForegroundColor Green
    Write-Host ""
    Write-Host "IMPORTANT:" -ForegroundColor Yellow
    Write-Host "• Ces modifications sont RÉELLES et permanentes" -ForegroundColor Yellow
    Write-Host "• Un redémarrage peut être nécessaire pour certains changements" -ForegroundColor Yellow
    Write-Host "• Utilisez uniquement à des fins éducatives" -ForegroundColor Yellow
} else {
    Write-Host "✗ Aucune modification n'a réussi" -ForegroundColor Red
    Write-Host ""
    Write-Host "Causes possibles:" -ForegroundColor Yellow
    Write-Host "• Antivirus bloquant les modifications" -ForegroundColor Yellow
    Write-Host "• Permissions insuffisantes" -ForegroundColor Yellow
    Write-Host "• Clés de registre protégées" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Appuyez sur Entrée pour quitter"
