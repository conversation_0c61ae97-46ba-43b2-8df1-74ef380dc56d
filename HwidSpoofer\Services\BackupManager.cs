using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using HwidSpoofer.Models;

namespace HwidSpoofer.Services
{
    /// <summary>
    /// Service de gestion des sauvegardes hardware
    /// </summary>
    public class BackupManager
    {
        private readonly string _backupDirectory;
        private readonly HardwareDetector _detector;
        private readonly JsonSerializerOptions _jsonOptions;

        public BackupManager()
        {
            _backupDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "HwidSpoofer",
                "Backups"
            );

            _detector = new HardwareDetector();
            
            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            // Créer le dossier de sauvegarde s'il n'existe pas
            Directory.CreateDirectory(_backupDirectory);
        }

        /// <summary>
        /// Crée une nouvelle sauvegarde du hardware actuel
        /// </summary>
        public HardwareOperationResult CreateBackup(string description)
        {
            try
            {
                var backup = new HardwareBackup(description);
                
                // Détecter le hardware actuel
                backup.HardwareItems = _detector.DetectAllHardware();
                
                // Ajouter des informations système
                backup.SystemInfo = GenerateSystemInfo();

                // Générer le nom de fichier
                var fileName = $"backup_{backup.CreatedAt:yyyyMMdd_HHmmss}.json";
                var filePath = Path.Combine(_backupDirectory, fileName);

                // Sérialiser et sauvegarder
                var json = JsonSerializer.Serialize(backup, _jsonOptions);
                File.WriteAllText(filePath, json);

                return HardwareOperationResult.CreateSuccess($"Sauvegarde créée: {fileName}");
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur lors de la création de sauvegarde: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Obtient la liste de toutes les sauvegardes disponibles
        /// </summary>
        public List<HardwareBackup> GetAvailableBackups()
        {
            var backups = new List<HardwareBackup>();

            try
            {
                if (!Directory.Exists(_backupDirectory))
                {
                    return backups;
                }

                var backupFiles = Directory.GetFiles(_backupDirectory, "backup_*.json")
                    .OrderByDescending(f => File.GetCreationTime(f));

                foreach (var file in backupFiles)
                {
                    try
                    {
                        var json = File.ReadAllText(file);
                        var backup = JsonSerializer.Deserialize<HardwareBackup>(json, _jsonOptions);
                        if (backup != null)
                        {
                            backups.Add(backup);
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log l'erreur mais continue avec les autres fichiers
                        Console.WriteLine($"Erreur lors du chargement de {file}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la lecture des sauvegardes: {ex.Message}");
            }

            return backups;
        }

        /// <summary>
        /// Restaure une sauvegarde spécifique
        /// </summary>
        public HardwareOperationResult RestoreBackup(DateTime backupDate)
        {
            try
            {
                var backup = GetBackupByDate(backupDate);
                if (backup == null)
                {
                    return HardwareOperationResult.CreateError($"Sauvegarde du {backupDate} non trouvée");
                }

                var registryManager = new RegistryManager();
                var errors = new List<string>();
                var restored = 0;

                foreach (var hardware in backup.HardwareItems.Values)
                {
                    if (!string.IsNullOrEmpty(hardware.RegistryPath) && !string.IsNullOrEmpty(hardware.RegistryKey))
                    {
                        var result = registryManager.SetRegistryValue(
                            hardware.RegistryPath,
                            hardware.RegistryKey,
                            hardware.OriginalValue
                        );

                        if (result.Success)
                        {
                            restored++;
                        }
                        else
                        {
                            errors.Add($"{hardware.Name}: {result.Message}");
                        }
                    }
                }

                if (errors.Count > 0)
                {
                    var errorMessage = $"Erreurs lors de la restauration:\n{string.Join("\n", errors)}";
                    var result = HardwareOperationResult.CreateError(errorMessage);
                    result.AddWarning($"{restored} éléments restaurés avec succès");
                    return result;
                }

                return HardwareOperationResult.CreateSuccess($"Sauvegarde restaurée avec succès: {restored} éléments");
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur lors de la restauration: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Supprime une sauvegarde
        /// </summary>
        public HardwareOperationResult DeleteBackup(DateTime backupDate)
        {
            try
            {
                var fileName = $"backup_{backupDate:yyyyMMdd_HHmmss}.json";
                var filePath = Path.Combine(_backupDirectory, fileName);

                if (!File.Exists(filePath))
                {
                    return HardwareOperationResult.CreateError($"Fichier de sauvegarde non trouvé: {fileName}");
                }

                File.Delete(filePath);
                return HardwareOperationResult.CreateSuccess($"Sauvegarde supprimée: {fileName}");
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur lors de la suppression: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Nettoie les anciennes sauvegardes (garde les 10 plus récentes)
        /// </summary>
        public HardwareOperationResult CleanupOldBackups(int keepCount = 10)
        {
            try
            {
                if (!Directory.Exists(_backupDirectory))
                {
                    return HardwareOperationResult.CreateSuccess("Aucune sauvegarde à nettoyer");
                }

                var backupFiles = Directory.GetFiles(_backupDirectory, "backup_*.json")
                    .OrderByDescending(f => File.GetCreationTime(f))
                    .ToList();

                if (backupFiles.Count <= keepCount)
                {
                    return HardwareOperationResult.CreateSuccess($"Aucun nettoyage nécessaire ({backupFiles.Count} sauvegardes)");
                }

                var filesToDelete = backupFiles.Skip(keepCount);
                var deletedCount = 0;

                foreach (var file in filesToDelete)
                {
                    try
                    {
                        File.Delete(file);
                        deletedCount++;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Erreur lors de la suppression de {file}: {ex.Message}");
                    }
                }

                return HardwareOperationResult.CreateSuccess($"Nettoyage terminé: {deletedCount} anciennes sauvegardes supprimées");
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur lors du nettoyage: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Exporte une sauvegarde vers un fichier spécifique
        /// </summary>
        public HardwareOperationResult ExportBackup(DateTime backupDate, string exportPath)
        {
            try
            {
                var backup = GetBackupByDate(backupDate);
                if (backup == null)
                {
                    return HardwareOperationResult.CreateError($"Sauvegarde du {backupDate} non trouvée");
                }

                var json = JsonSerializer.Serialize(backup, _jsonOptions);
                File.WriteAllText(exportPath, json);

                return HardwareOperationResult.CreateSuccess($"Sauvegarde exportée vers: {exportPath}");
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur lors de l'export: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Importe une sauvegarde depuis un fichier
        /// </summary>
        public HardwareOperationResult ImportBackup(string importPath)
        {
            try
            {
                if (!File.Exists(importPath))
                {
                    return HardwareOperationResult.CreateError($"Fichier non trouvé: {importPath}");
                }

                var json = File.ReadAllText(importPath);
                var backup = JsonSerializer.Deserialize<HardwareBackup>(json, _jsonOptions);

                if (backup == null)
                {
                    return HardwareOperationResult.CreateError("Format de sauvegarde invalide");
                }

                // Générer un nouveau nom de fichier pour éviter les conflits
                var fileName = $"backup_imported_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                var filePath = Path.Combine(_backupDirectory, fileName);

                File.WriteAllText(filePath, json);

                return HardwareOperationResult.CreateSuccess($"Sauvegarde importée: {fileName}");
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur lors de l'import: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Obtient une sauvegarde par date
        /// </summary>
        private HardwareBackup? GetBackupByDate(DateTime backupDate)
        {
            var fileName = $"backup_{backupDate:yyyyMMdd_HHmmss}.json";
            var filePath = Path.Combine(_backupDirectory, fileName);

            if (!File.Exists(filePath))
            {
                return null;
            }

            try
            {
                var json = File.ReadAllText(filePath);
                return JsonSerializer.Deserialize<HardwareBackup>(json, _jsonOptions);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Génère des informations système pour la sauvegarde
        /// </summary>
        private string GenerateSystemInfo()
        {
            try
            {
                return $"OS: {Environment.OSVersion}\n" +
                       $"Machine: {Environment.MachineName}\n" +
                       $"User: {Environment.UserName}\n" +
                       $"CLR: {Environment.Version}\n" +
                       $"Processors: {Environment.ProcessorCount}\n" +
                       $"Working Set: {Environment.WorkingSet / 1024 / 1024} MB";
            }
            catch
            {
                return "Informations système non disponibles";
            }
        }

        /// <summary>
        /// Obtient la taille totale des sauvegardes
        /// </summary>
        public long GetTotalBackupSize()
        {
            try
            {
                if (!Directory.Exists(_backupDirectory))
                {
                    return 0;
                }

                return Directory.GetFiles(_backupDirectory, "backup_*.json")
                    .Sum(file => new FileInfo(file).Length);
            }
            catch
            {
                return 0;
            }
        }
    }
}
