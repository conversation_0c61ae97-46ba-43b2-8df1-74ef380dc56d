# Script PowerShell pour tester l'application HWID Spoofer
# ATTENTION: Utilisation éducative uniquement

Write-Host "========================================" -ForegroundColor Yellow
Write-Host "    Test HWID Spoofer - Outil Éducatif" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
Write-Host ""

# Vérifier si on est administrateur
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "ERREUR: Ce script doit être exécuté en tant qu'administrateur." -ForegroundColor Red
    Write-Host "Clic droit sur PowerShell > 'Exécuter en tant qu'administrateur'" -ForegroundColor Yellow
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

Write-Host "✓ Privilèges administrateur détectés" -ForegroundColor Green
Write-Host ""

# Vérifier la présence de .NET 6.0
Write-Host "Vérification de .NET 6.0..." -ForegroundColor Cyan
try {
    $dotnetVersion = dotnet --version
    Write-Host "✓ .NET version détectée: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ .NET 6.0 non trouvé. Veuillez l'installer depuis https://dotnet.microsoft.com/download" -ForegroundColor Red
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

Write-Host ""

# Vérifier la structure du projet
Write-Host "Vérification de la structure du projet..." -ForegroundColor Cyan

$requiredFiles = @(
    "HwidSpoofer.sln",
    "HwidSpoofer\HwidSpoofer.csproj",
    "HwidSpoofer\Program.cs",
    "HwidSpoofer\MainForm.cs"
)

$allFilesExist = $true
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file" -ForegroundColor Green
    } else {
        Write-Host "✗ $file manquant" -ForegroundColor Red
        $allFilesExist = $false
    }
}

if (-not $allFilesExist) {
    Write-Host ""
    Write-Host "ERREUR: Fichiers manquants. Vérifiez la structure du projet." -ForegroundColor Red
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

Write-Host ""

# Compilation du projet
Write-Host "Compilation du projet..." -ForegroundColor Cyan
try {
    $buildResult = dotnet build HwidSpoofer.sln --configuration Release --verbosity quiet
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Compilation réussie" -ForegroundColor Green
    } else {
        Write-Host "✗ Erreur de compilation" -ForegroundColor Red
        Write-Host $buildResult -ForegroundColor Red
        Read-Host "Appuyez sur Entrée pour quitter"
        exit 1
    }
} catch {
    Write-Host "✗ Erreur lors de la compilation: $_" -ForegroundColor Red
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

Write-Host ""

# Vérifier l'exécutable
$exePath = "HwidSpoofer\bin\Release\net6.0-windows\HwidSpoofer.exe"
if (Test-Path $exePath) {
    Write-Host "✓ Exécutable trouvé: $exePath" -ForegroundColor Green
} else {
    Write-Host "✗ Exécutable non trouvé: $exePath" -ForegroundColor Red
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

Write-Host ""

# Afficher les avertissements
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "           AVERTISSEMENTS" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
Write-Host ""
Write-Host "⚠️  Cet outil est destiné UNIQUEMENT à des fins éducatives" -ForegroundColor Red
Write-Host "⚠️  N'utilisez PAS pour contourner des bans ou restrictions" -ForegroundColor Red
Write-Host "⚠️  L'auteur n'est PAS responsable de l'usage malveillant" -ForegroundColor Red
Write-Host "⚠️  Respectez les lois locales et conditions d'utilisation" -ForegroundColor Red
Write-Host ""

# Demander confirmation
$confirmation = Read-Host "Acceptez-vous ces conditions et voulez-vous lancer l'application ? (oui/non)"

if ($confirmation -eq "oui" -or $confirmation -eq "o" -or $confirmation -eq "yes" -or $confirmation -eq "y") {
    Write-Host ""
    Write-Host "Lancement de l'application..." -ForegroundColor Green
    Write-Host ""
    
    try {
        # Lancer l'application
        Start-Process -FilePath $exePath -Wait
        Write-Host ""
        Write-Host "Application fermée." -ForegroundColor Cyan
    } catch {
        Write-Host "Erreur lors du lancement: $_" -ForegroundColor Red
    }
} else {
    Write-Host ""
    Write-Host "Lancement annulé par l'utilisateur." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Test terminé." -ForegroundColor Cyan
Read-Host "Appuyez sur Entrée pour quitter"
