using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using HwidSpoofer.Models;
using HwidSpoofer.Services;

namespace HwidSpoofer.Forms
{
    /// <summary>
    /// Interface utilisateur avancée avec mode expert
    /// </summary>
    public partial class AdvancedMainForm : Form
    {
        private readonly HwidManager _hwidManager;
        private readonly UsbDeviceManager _usbManager;
        private readonly AdvancedDiskManager _diskManager;
        private readonly GraphicsDeviceManager _gpuManager;
        
        // Contrôles UI
        private TabControl _mainTabControl = null!;
        private MenuStrip _menuStrip = null!;
        private StatusStrip _statusStrip = null!;
        private ToolStripStatusLabel _statusLabel = null!;
        private ToolStripProgressBar _progressBar = null!;
        
        // Mode expert
        private bool _expertMode = false;
        private Panel _expertPanel = null!;
        
        // Thème sombre
        private bool _darkTheme = false;

        public AdvancedMainForm()
        {
            _hwidManager = new HwidManager();
            _usbManager = new UsbDeviceManager(new RegistryManager());
            _diskManager = new AdvancedDiskManager(new RegistryManager());
            _gpuManager = new GraphicsDeviceManager(new RegistryManager());
            
            InitializeAdvancedComponent();
            SetupAdvancedEventHandlers();
            InitializeManagers();
        }

        private void InitializeAdvancedComponent()
        {
            // Configuration de la fenêtre
            Text = "HWID Spoofer Pro - Outil Éducatif Avancé v2.0";
            Size = new Size(1400, 900);
            StartPosition = FormStartPosition.CenterScreen;
            MinimumSize = new Size(1200, 700);
            Icon = SystemIcons.Shield;

            // Menu principal
            CreateMenuStrip();
            
            // TabControl principal avec plus d'onglets
            CreateAdvancedTabControl();
            
            // Barre de statut améliorée
            CreateAdvancedStatusStrip();
            
            // Panel expert (masqué par défaut)
            CreateExpertPanel();
            
            // Avertissement de sécurité
            CreateSecurityWarning();
            
            // Appliquer le thème par défaut
            ApplyTheme();
        }

        private void CreateMenuStrip()
        {
            _menuStrip = new MenuStrip();
            
            // Menu Fichier
            var fileMenu = new ToolStripMenuItem("&Fichier");
            fileMenu.DropDownItems.Add("&Nouveau Profil", null, OnNewProfile);
            fileMenu.DropDownItems.Add("&Charger Profil", null, OnLoadProfile);
            fileMenu.DropDownItems.Add("&Sauvegarder Profil", null, OnSaveProfile);
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add("&Exporter Rapport", null, OnExportReport);
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add("&Quitter", null, (s, e) => Close());
            
            // Menu Outils
            var toolsMenu = new ToolStripMenuItem("&Outils");
            toolsMenu.DropDownItems.Add("&Scanner Complet", null, OnFullScan);
            toolsMenu.DropDownItems.Add("&Diagnostic Système", null, OnSystemDiagnostic);
            toolsMenu.DropDownItems.Add("&Vérifier Intégrité", null, OnCheckIntegrity);
            toolsMenu.DropDownItems.Add(new ToolStripSeparator());
            toolsMenu.DropDownItems.Add("&Nettoyer Registre", null, OnCleanRegistry);
            
            // Menu Mode
            var modeMenu = new ToolStripMenuItem("&Mode");
            var expertModeItem = new ToolStripMenuItem("Mode &Expert") { CheckOnClick = true };
            expertModeItem.CheckedChanged += OnExpertModeToggle;
            modeMenu.DropDownItems.Add(expertModeItem);
            
            var darkThemeItem = new ToolStripMenuItem("Thème &Sombre") { CheckOnClick = true };
            darkThemeItem.CheckedChanged += OnDarkThemeToggle;
            modeMenu.DropDownItems.Add(darkThemeItem);
            
            // Menu Aide
            var helpMenu = new ToolStripMenuItem("&Aide");
            helpMenu.DropDownItems.Add("&Documentation", null, OnShowDocumentation);
            helpMenu.DropDownItems.Add("&Mentions Légales", null, OnShowLegal);
            helpMenu.DropDownItems.Add(new ToolStripSeparator());
            helpMenu.DropDownItems.Add("À &Propos", null, OnShowAbout);
            
            _menuStrip.Items.AddRange(new ToolStripItem[] { fileMenu, toolsMenu, modeMenu, helpMenu });
            Controls.Add(_menuStrip);
            MainMenuStrip = _menuStrip;
        }

        private void CreateAdvancedTabControl()
        {
            _mainTabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 9F),
                Appearance = TabAppearance.FlatButtons,
                Multiline = true
            };

            // Onglet Hardware Standard
            var standardTab = new TabPage("🖥️ Hardware Standard");
            CreateStandardHardwareTab(standardTab);
            _mainTabControl.TabPages.Add(standardTab);

            // Onglet Périphériques USB
            var usbTab = new TabPage("🔌 Périphériques USB");
            CreateUsbDevicesTab(usbTab);
            _mainTabControl.TabPages.Add(usbTab);

            // Onglet Disques Avancé
            var diskTab = new TabPage("💾 Stockage Avancé");
            CreateAdvancedDiskTab(diskTab);
            _mainTabControl.TabPages.Add(diskTab);

            // Onglet Cartes Graphiques
            var gpuTab = new TabPage("🎮 Cartes Graphiques");
            CreateGraphicsTab(gpuTab);
            _mainTabControl.TabPages.Add(gpuTab);

            // Onglet Réseau Avancé
            var networkTab = new TabPage("🌐 Réseau Avancé");
            CreateAdvancedNetworkTab(networkTab);
            _mainTabControl.TabPages.Add(networkTab);

            // Onglet Profils
            var profilesTab = new TabPage("📋 Profils");
            CreateProfilesTab(profilesTab);
            _mainTabControl.TabPages.Add(profilesTab);

            // Onglet Logs Avancés
            var logsTab = new TabPage("📝 Logs & Diagnostic");
            CreateAdvancedLogsTab(logsTab);
            _mainTabControl.TabPages.Add(logsTab);

            Controls.Add(_mainTabControl);
        }

        private void CreateAdvancedStatusStrip()
        {
            _statusStrip = new StatusStrip();
            
            _statusLabel = new ToolStripStatusLabel("Prêt")
            {
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };
            
            _progressBar = new ToolStripProgressBar
            {
                Size = new Size(200, 16),
                Visible = false
            };
            
            var modeLabel = new ToolStripStatusLabel("Mode: Standard")
            {
                Name = "ModeLabel"
            };
            
            var themeLabel = new ToolStripStatusLabel("Thème: Clair")
            {
                Name = "ThemeLabel"
            };
            
            _statusStrip.Items.AddRange(new ToolStripItem[] 
            { 
                _statusLabel, _progressBar, modeLabel, themeLabel 
            });
            
            Controls.Add(_statusStrip);
        }

        private void CreateExpertPanel()
        {
            _expertPanel = new Panel
            {
                Height = 100,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(240, 240, 240),
                BorderStyle = BorderStyle.FixedSingle,
                Visible = false
            };

            var expertLabel = new Label
            {
                Text = "🔧 MODE EXPERT ACTIVÉ",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.DarkRed,
                Location = new Point(10, 10),
                AutoSize = true
            };

            var warningLabel = new Label
            {
                Text = "Attention: Les fonctionnalités avancées peuvent affecter la stabilité du système.\n" +
                       "Utilisez uniquement si vous comprenez les implications techniques.",
                Font = new Font("Segoe UI", 8F),
                ForeColor = Color.DarkOrange,
                Location = new Point(10, 35),
                Size = new Size(800, 40)
            };

            // Boutons expert
            var registryEditorBtn = new Button
            {
                Text = "Éditeur Registre",
                Size = new Size(120, 25),
                Location = new Point(10, 70)
            };
            registryEditorBtn.Click += OnOpenRegistryEditor;

            var rawEditBtn = new Button
            {
                Text = "Édition Brute",
                Size = new Size(120, 25),
                Location = new Point(140, 70)
            };
            rawEditBtn.Click += OnRawEdit;

            var systemInfoBtn = new Button
            {
                Text = "Info Système",
                Size = new Size(120, 25),
                Location = new Point(270, 70)
            };
            systemInfoBtn.Click += OnSystemInfo;

            _expertPanel.Controls.AddRange(new Control[] 
            { 
                expertLabel, warningLabel, registryEditorBtn, rawEditBtn, systemInfoBtn 
            });

            Controls.Add(_expertPanel);
        }

        private void CreateSecurityWarning()
        {
            var warningPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(255, 255, 200),
                BorderStyle = BorderStyle.FixedSingle
            };

            var warningIcon = new Label
            {
                Text = "⚠️",
                Font = new Font("Segoe UI", 24F),
                Location = new Point(10, 20),
                Size = new Size(50, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var warningText = new Label
            {
                Text = "HWID Spoofer Pro - Outil Éducatif Avancé\n" +
                       "Destiné uniquement à l'apprentissage et à la recherche en sécurité informatique.\n" +
                       "L'utilisation malveillante est strictement interdite et peut être illégale.",
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.DarkRed,
                Location = new Point(70, 10),
                Size = new Size(800, 60),
                TextAlign = ContentAlignment.MiddleLeft
            };

            var closeWarningBtn = new Button
            {
                Text = "✕",
                Size = new Size(25, 25),
                Location = new Point(warningPanel.Width - 35, 10),
                Anchor = AnchorStyles.Top | AnchorStyles.Right,
                FlatStyle = FlatStyle.Flat
            };
            closeWarningBtn.Click += (s, e) => warningPanel.Visible = false;

            warningPanel.Controls.AddRange(new Control[] { warningIcon, warningText, closeWarningBtn });
            Controls.Add(warningPanel);
            warningPanel.BringToFront();
        }

        private void ApplyTheme()
        {
            if (_darkTheme)
            {
                // Thème sombre
                BackColor = Color.FromArgb(45, 45, 48);
                ForeColor = Color.White;
                _mainTabControl.BackColor = Color.FromArgb(37, 37, 38);
                _mainTabControl.ForeColor = Color.White;
                _statusStrip.BackColor = Color.FromArgb(0, 122, 204);
                _statusStrip.ForeColor = Color.White;
                _menuStrip.BackColor = Color.FromArgb(45, 45, 48);
                _menuStrip.ForeColor = Color.White;
            }
            else
            {
                // Thème clair
                BackColor = SystemColors.Control;
                ForeColor = SystemColors.ControlText;
                _mainTabControl.BackColor = SystemColors.Control;
                _mainTabControl.ForeColor = SystemColors.ControlText;
                _statusStrip.BackColor = SystemColors.Control;
                _statusStrip.ForeColor = SystemColors.ControlText;
                _menuStrip.BackColor = SystemColors.Control;
                _menuStrip.ForeColor = SystemColors.ControlText;
            }

            // Mettre à jour le label de thème
            var themeLabel = _statusStrip.Items["ThemeLabel"] as ToolStripStatusLabel;
            if (themeLabel != null)
            {
                themeLabel.Text = _darkTheme ? "Thème: Sombre" : "Thème: Clair";
            }
        }

        // Méthodes pour créer les onglets (à implémenter)
        private void CreateStandardHardwareTab(TabPage tab) { /* Implémentation existante */ }
        private void CreateUsbDevicesTab(TabPage tab) { /* Nouvelle implémentation USB */ }
        private void CreateAdvancedDiskTab(TabPage tab) { /* Nouvelle implémentation disques */ }
        private void CreateGraphicsTab(TabPage tab) { /* Nouvelle implémentation GPU */ }
        private void CreateAdvancedNetworkTab(TabPage tab) { /* Implémentation réseau avancée */ }
        private void CreateProfilesTab(TabPage tab) { /* Gestion des profils */ }
        private void CreateAdvancedLogsTab(TabPage tab) { /* Logs avancés avec filtres */ }

        // Event handlers pour les menus
        private void OnNewProfile(object? sender, EventArgs e) { /* Nouveau profil */ }
        private void OnLoadProfile(object? sender, EventArgs e) { /* Charger profil */ }
        private void OnSaveProfile(object? sender, EventArgs e) { /* Sauvegarder profil */ }
        private void OnExportReport(object? sender, EventArgs e) { /* Exporter rapport */ }
        private void OnFullScan(object? sender, EventArgs e) { /* Scanner complet */ }
        private void OnSystemDiagnostic(object? sender, EventArgs e) { /* Diagnostic système */ }
        private void OnCheckIntegrity(object? sender, EventArgs e) { /* Vérifier intégrité */ }
        private void OnCleanRegistry(object? sender, EventArgs e) { /* Nettoyer registre */ }
        private void OnShowDocumentation(object? sender, EventArgs e) { /* Documentation */ }
        private void OnShowLegal(object? sender, EventArgs e) { /* Mentions légales */ }
        private void OnShowAbout(object? sender, EventArgs e) { /* À propos */ }
        private void OnOpenRegistryEditor(object? sender, EventArgs e) { /* Éditeur registre */ }
        private void OnRawEdit(object? sender, EventArgs e) { /* Édition brute */ }
        private void OnSystemInfo(object? sender, EventArgs e) { /* Info système */ }

        private void OnExpertModeToggle(object? sender, EventArgs e)
        {
            var menuItem = sender as ToolStripMenuItem;
            _expertMode = menuItem?.Checked ?? false;
            _expertPanel.Visible = _expertMode;
            
            var modeLabel = _statusStrip.Items["ModeLabel"] as ToolStripStatusLabel;
            if (modeLabel != null)
            {
                modeLabel.Text = _expertMode ? "Mode: Expert" : "Mode: Standard";
            }
        }

        private void OnDarkThemeToggle(object? sender, EventArgs e)
        {
            var menuItem = sender as ToolStripMenuItem;
            _darkTheme = menuItem?.Checked ?? false;
            ApplyTheme();
        }

        private void SetupAdvancedEventHandlers()
        {
            // Event handlers pour les gestionnaires
            _hwidManager.LogMessage += OnLogMessage;
            _hwidManager.OperationCompleted += OnOperationCompleted;
            
            FormClosing += OnAdvancedFormClosing;
        }

        private void InitializeManagers()
        {
            try
            {
                ShowProgress("Initialisation des gestionnaires...");
                
                var result = _hwidManager.Initialize();
                if (result.Success)
                {
                    _statusLabel.Text = "Gestionnaires initialisés avec succès";
                }
                else
                {
                    MessageBox.Show(
                        $"Erreur lors de l'initialisation:\n{result.Message}",
                        "Erreur",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error
                    );
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Erreur critique lors de l'initialisation:\n{ex.Message}",
                    "Erreur Critique",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
            finally
            {
                HideProgress();
            }
        }

        private void ShowProgress(string message)
        {
            _statusLabel.Text = message;
            _progressBar.Visible = true;
            _progressBar.Style = ProgressBarStyle.Marquee;
            Application.DoEvents();
        }

        private void HideProgress()
        {
            _progressBar.Visible = false;
            _progressBar.Style = ProgressBarStyle.Blocks;
        }

        private void OnLogMessage(object? sender, string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<object?, string>(OnLogMessage), sender, message);
                return;
            }
            
            // Ajouter aux logs avancés
            // TODO: Implémenter le système de logs avancé
        }

        private void OnOperationCompleted(object? sender, HardwareOperationResult result)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<object?, HardwareOperationResult>(OnOperationCompleted), sender, result);
                return;
            }

            _statusLabel.Text = result.Message;
            
            if (!result.Success)
            {
                MessageBox.Show(
                    result.Message,
                    "Erreur d'Opération",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );
            }
        }

        private void OnAdvancedFormClosing(object? sender, FormClosingEventArgs e)
        {
            // Vérifications avancées avant fermeture
            // TODO: Implémenter les vérifications de sécurité
        }
    }
}
