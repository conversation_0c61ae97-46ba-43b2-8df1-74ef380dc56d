@echo off
setlocal enabledelayedexpansion

:: ========================================
::    HWID RESTORE - Restauration Complete
:: ========================================
:: Restaure tous les identifiants depuis la sauvegarde
:: ========================================

title HWID Restore - Restauration Complete

:: Couleurs
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "RESET=[0m"

echo %CYAN%========================================%RESET%
echo %CYAN%    HWID RESTORE - Restauration%RESET%
echo %CYAN%========================================%RESET%
echo.

:: Vérifier les privilèges administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo %RED%ERREUR: Privileges administrateur requis%RESET%
    pause
    exit /b 1
)

echo %GREEN%✓ Privileges administrateur detectes%RESET%
echo.

:: Chercher les dossiers de sauvegarde
echo %BLUE%Recherche des sauvegardes...%RESET%
set "BACKUP_COUNT=0"
for /d %%d in ("%USERPROFILE%\Desktop\HWID_Backup_*") do (
    set /a "BACKUP_COUNT+=1"
    echo %YELLOW%[!BACKUP_COUNT!] %%~nxd%RESET%
    set "BACKUP_!BACKUP_COUNT!=%%d"
)

if %BACKUP_COUNT% equ 0 (
    echo %RED%Aucune sauvegarde trouvee sur le bureau%RESET%
    pause
    exit /b 1
)

echo.
set /p "choice=Choisissez une sauvegarde (1-%BACKUP_COUNT%): "

:: Valider le choix
if %choice% lss 1 goto :InvalidChoice
if %choice% gtr %BACKUP_COUNT% goto :InvalidChoice

:: Sélectionner la sauvegarde
call set "BACKUP_DIR=%%BACKUP_%choice%%%"
echo.
echo %BLUE%Sauvegarde selectionnee: %BACKUP_DIR%%RESET%
echo.

:: Confirmer la restauration
echo %YELLOW%ATTENTION: Cette operation va restaurer tous les identifiants%RESET%
echo %YELLOW%depuis la sauvegarde selectionnee.%RESET%
echo.
set /p "confirm=Continuer ? (OUI/non): "
if /i not "%confirm%"=="OUI" (
    echo %YELLOW%Operation annulee%RESET%
    pause
    exit /b 0
)

echo.
echo %CYAN%========================================%RESET%
echo %CYAN%    DEBUT DE LA RESTAURATION%RESET%
echo %CYAN%========================================%RESET%
echo.

:: ========================================
:: 1. RESTAURER MACHINE GUID
:: ========================================
echo %BLUE%[1/7] Restauration du Machine GUID...%RESET%
if exist "%BACKUP_DIR%\original_machine_guid.txt" (
    set /p ORIGINAL_GUID=<"%BACKUP_DIR%\original_machine_guid.txt"
    reg add "HKLM\SOFTWARE\Microsoft\Cryptography" /v MachineGuid /t REG_SZ /d "!ORIGINAL_GUID!" /f >nul 2>&1
    if !errorLevel! equ 0 (
        echo %GREEN%   ✓ Machine GUID restaure: !ORIGINAL_GUID!%RESET%
    ) else (
        echo %RED%   ✗ Echec restauration Machine GUID%RESET%
    )
) else (
    echo %YELLOW%   ⚠ Fichier de sauvegarde Machine GUID non trouve%RESET%
)

:: ========================================
:: 2. RESTAURER NOM D'ORDINATEUR
:: ========================================
echo %BLUE%[2/7] Restauration du nom d'ordinateur...%RESET%
if exist "%BACKUP_DIR%\original_computer_name.txt" (
    set /p ORIGINAL_NAME=<"%BACKUP_DIR%\original_computer_name.txt"
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName" /v ComputerName /t REG_SZ /d "!ORIGINAL_NAME!" /f >nul 2>&1
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\ComputerName\ActiveComputerName" /v ComputerName /t REG_SZ /d "!ORIGINAL_NAME!" /f >nul 2>&1
    if !errorLevel! equ 0 (
        echo %GREEN%   ✓ Nom d'ordinateur restaure: !ORIGINAL_NAME!%RESET%
    ) else (
        echo %RED%   ✗ Echec restauration nom d'ordinateur%RESET%
    )
) else (
    echo %YELLOW%   ⚠ Fichier de sauvegarde nom d'ordinateur non trouve%RESET%
)

:: ========================================
:: 3. RESTAURER PRODUCT ID
:: ========================================
echo %BLUE%[3/7] Restauration du Product ID...%RESET%
if exist "%BACKUP_DIR%\original_product_id.txt" (
    set /p ORIGINAL_PRODUCT=<"%BACKUP_DIR%\original_product_id.txt"
    reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion" /v ProductId /t REG_SZ /d "!ORIGINAL_PRODUCT!" /f >nul 2>&1
    if !errorLevel! equ 0 (
        echo %GREEN%   ✓ Product ID restaure: !ORIGINAL_PRODUCT!%RESET%
    ) else (
        echo %RED%   ✗ Echec restauration Product ID%RESET%
    )
) else (
    echo %YELLOW%   ⚠ Fichier de sauvegarde Product ID non trouve%RESET%
)

:: ========================================
:: 4. RESTAURER PROPRIETAIRE
:: ========================================
echo %BLUE%[4/7] Restauration du proprietaire...%RESET%
if exist "%BACKUP_DIR%\original_registered_owner.txt" (
    set /p ORIGINAL_OWNER=<"%BACKUP_DIR%\original_registered_owner.txt"
    reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion" /v RegisteredOwner /t REG_SZ /d "!ORIGINAL_OWNER!" /f >nul 2>&1
    if !errorLevel! equ 0 (
        echo %GREEN%   ✓ Proprietaire restaure: !ORIGINAL_OWNER!%RESET%
    ) else (
        echo %RED%   ✗ Echec restauration proprietaire%RESET%
    )
) else (
    echo %YELLOW%   ⚠ Fichier de sauvegarde proprietaire non trouve%RESET%
)

:: ========================================
:: 5. RESTAURER ADRESSES MAC
:: ========================================
echo %BLUE%[5/7] Restauration des adresses MAC...%RESET%
if exist "%BACKUP_DIR%\original_mac_addresses.txt" (
    set "MAC_RESTORED=0"
    for /f "tokens=1,3 delims=-" %%a in ('type "%BACKUP_DIR%\original_mac_addresses.txt" ^| findstr "HKEY"') do (
        set "REG_KEY=%%a"
        set "MAC_VALUE=%%b"
        set "MAC_VALUE=!MAC_VALUE: =!"
        
        if not "!MAC_VALUE!"=="" (
            reg add "!REG_KEY!" /v NetworkAddress /t REG_SZ /d "!MAC_VALUE!" /f >nul 2>&1
            if !errorLevel! equ 0 (
                echo %GREEN%   ✓ MAC restauree: !MAC_VALUE!%RESET%
                set /a "MAC_RESTORED+=1"
            )
        )
    )
    echo %GREEN%   ✓ %MAC_RESTORED% adresses MAC restaurees%RESET%
) else (
    echo %YELLOW%   ⚠ Fichier de sauvegarde MAC non trouve%RESET%
)

:: ========================================
:: 6. RESTAURER IDENTIFIANTS DISQUES
:: ========================================
echo %BLUE%[6/7] Restauration des identifiants de disques...%RESET%
if exist "%BACKUP_DIR%\original_disk_ids.txt" (
    set "DISK_RESTORED=0"
    for /f "tokens=1,3* delims=-" %%a in ('type "%BACKUP_DIR%\original_disk_ids.txt" ^| findstr "HKEY"') do (
        set "REG_KEY=%%a"
        set "DISK_NAME=%%b %%c"
        set "DISK_NAME=!DISK_NAME: =!"
        
        if not "!DISK_NAME!"=="" (
            reg add "!REG_KEY!" /v FriendlyName /t REG_SZ /d "!DISK_NAME!" /f >nul 2>&1
            if !errorLevel! equ 0 (
                echo %GREEN%   ✓ Disque restaure: !DISK_NAME!%RESET%
                set /a "DISK_RESTORED+=1"
            )
        )
    )
    echo %GREEN%   ✓ %DISK_RESTORED% disques restaures%RESET%
) else (
    echo %YELLOW%   ⚠ Fichier de sauvegarde disques non trouve%RESET%
)

:: ========================================
:: 7. RESTAURER IDENTIFIANTS USB
:: ========================================
echo %BLUE%[7/7] Restauration des identifiants USB...%RESET%
if exist "%BACKUP_DIR%\original_usb_ids.txt" (
    set "USB_RESTORED=0"
    for /f "tokens=1,3* delims=-" %%a in ('type "%BACKUP_DIR%\original_usb_ids.txt" ^| findstr "HKEY"') do (
        set "REG_KEY=%%a"
        set "USB_NAME=%%b %%c"
        set "USB_NAME=!USB_NAME: =!"
        
        if not "!USB_NAME!"=="" (
            reg add "!REG_KEY!" /v DeviceDesc /t REG_SZ /d "!USB_NAME!" /f >nul 2>&1
            if !errorLevel! equ 0 (
                echo %GREEN%   ✓ USB restaure: !USB_NAME!%RESET%
                set /a "USB_RESTORED+=1"
            )
        )
    )
    echo %GREEN%   ✓ %USB_RESTORED% peripheriques USB restaures%RESET%
) else (
    echo %YELLOW%   ⚠ Fichier de sauvegarde USB non trouve%RESET%
)

:: ========================================
:: FINALISATION
:: ========================================
echo.
echo %CYAN%========================================%RESET%
echo %CYAN%        RESTAURATION TERMINEE%RESET%
echo %CYAN%========================================%RESET%
echo.
echo %GREEN%✓ Tous les identifiants ont ete restaures depuis:%RESET%
echo %BLUE%  %BACKUP_DIR%%RESET%
echo.
echo %RED%⚠️  REDEMARRAGE OBLIGATOIRE pour finaliser la restauration%RESET%
echo.

:: Proposer le redémarrage
set /p "restart=Redemarrer maintenant ? (O/n): "
if /i "%restart%"=="O" (
    echo %YELLOW%Redemarrage en cours...%RESET%
    shutdown /r /t 10 /c "Redemarrage pour finaliser la restauration HWID"
) else (
    echo %YELLOW%Pensez a redemarrer manuellement%RESET%
)

echo.
pause
goto :EOF

:InvalidChoice
echo %RED%Choix invalide%RESET%
pause
exit /b 1
