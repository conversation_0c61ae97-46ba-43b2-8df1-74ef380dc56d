# Documentation Technique - HWID Spoofer

## 🏗️ Architecture du Logiciel

### Vue d'ensemble
Le HWID Spoofer est une application Windows Forms développée en C# .NET 6.0 qui permet la détection, la modification et la restauration des identifiants hardware du système.

### Structure des Composants

```
HwidSpoofer/
├── Models/
│   └── HardwareInfo.cs          # Modèles de données
├── Services/
│   ├── HardwareDetector.cs      # Détection hardware via WMI
│   ├── HwidManager.cs           # Gestionnaire principal
│   ├── RegistryManager.cs       # Gestion du registre Windows
│   └── BackupManager.cs         # Système de sauvegarde
├── MainForm.cs                  # Interface utilisateur principale
└── Program.cs                   # Point d'entrée de l'application
```

## 🔍 Détection Hardware

### Technologies Utilisées
- **WMI (Windows Management Instrumentation)** : Accès aux informations hardware
- **Registre Windows** : Lecture/écriture des identifiants système
- **System.Management** : Interface .NET pour WMI

### Identifiants Détectés

#### Processeur
- **CPU ID** : Identifiant unique du processeur
- **Nom** : Modèle et marque du processeur
- **Registre** : `HKLM\HARDWARE\DESCRIPTION\System\CentralProcessor\0`

#### Carte Mère
- **Numéro de série** : Identifiant unique de la carte mère
- **Modèle** : Référence du produit
- **Registre** : `HKLM\HARDWARE\DESCRIPTION\System\BIOS`

#### Stockage
- **Numéros de série** : Identifiants des disques durs/SSD
- **Modèles** : Références des périphériques de stockage
- **Registre** : `HKLM\HARDWARE\DEVICEMAP\Scsi`

#### Réseau
- **Adresses MAC** : Identifiants des cartes réseau physiques
- **Noms** : Descriptions des adaptateurs
- **Registre** : `HKLM\SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}`

#### BIOS/UEFI
- **Numéro de série BIOS** : Identifiant du firmware
- **Version** : Version du BIOS/UEFI
- **Registre** : `HKLM\HARDWARE\DESCRIPTION\System\BIOS`

#### Système
- **UUID Système** : Identifiant unique universel
- **GUID Machine** : Identifiant généré par Windows
- **Registre** : `HKLM\SOFTWARE\Microsoft\Cryptography`

## 🛠️ Fonctionnalités Techniques

### Détection Automatique
```csharp
// Exemple de détection via WMI
using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor");
foreach (ManagementObject obj in searcher.Get())
{
    var processorId = obj["ProcessorId"]?.ToString();
    // Traitement de l'identifiant
}
```

### Modification Sécurisée
- **Sauvegarde automatique** des valeurs originales
- **Vérification d'intégrité** avant modification
- **Rollback automatique** en cas d'erreur
- **Validation des permissions** administrateur

### Génération d'Identifiants
```csharp
// Génération d'identifiants aléatoires selon le type
public string GenerateRandomValue(string hardwareType)
{
    return hardwareType.ToUpper() switch
    {
        "CPU_ID" => GenerateRandomHex(16),
        "MOTHERBOARD_SERIAL" => $"MB{GenerateRandomAlphaNumeric(8)}",
        "SYSTEM_UUID" => GenerateRandomGuid(),
        // ...
    };
}
```

## 💾 Système de Sauvegarde

### Format de Sauvegarde
Les sauvegardes sont stockées au format JSON dans :
```
%APPDATA%\HwidSpoofer\Backups\backup_YYYYMMDD_HHMMSS.json
```

### Structure JSON
```json
{
  "createdAt": "2025-06-15T10:30:00Z",
  "description": "Sauvegarde automatique",
  "hardwareItems": {
    "CPU_ID": {
      "name": "ID Processeur",
      "category": "Processor",
      "currentValue": "BFEBFBFF000906EA",
      "originalValue": "BFEBFBFF000906EA",
      "registryPath": "HKEY_LOCAL_MACHINE\\HARDWARE\\...",
      "registryKey": "Identifier"
    }
  },
  "systemInfo": "OS: Windows 11..."
}
```

### Fonctionnalités de Sauvegarde
- **Création automatique** au démarrage
- **Sauvegardes manuelles** avec description
- **Restauration sélective** par date
- **Nettoyage automatique** (garde les 10 plus récentes)
- **Export/Import** de sauvegardes

## 🔐 Sécurité et Permissions

### Privilèges Requis
- **Administrateur** : Obligatoire pour l'accès au registre
- **WMI** : Lecture des informations hardware
- **Registre** : Lecture/écriture des clés système

### Mesures de Protection
```csharp
// Vérification des privilèges administrateur
private static bool IsRunningAsAdministrator()
{
    WindowsIdentity identity = WindowsIdentity.GetCurrent();
    WindowsPrincipal principal = new WindowsPrincipal(identity);
    return principal.IsInRole(WindowsBuiltInRole.Administrator);
}
```

### Validation des Modifications
- **Vérification d'existence** des clés de registre
- **Sauvegarde préalable** obligatoire
- **Validation des types** de données
- **Gestion des erreurs** avec rollback

## 🎯 Interface Utilisateur

### Architecture MVVM Simplifiée
- **MainForm** : Vue principale avec onglets
- **HwidManager** : Contrôleur principal
- **Models** : Données et logique métier

### Onglets Principaux

#### Hardware Détecté
- **ListView** avec colonnes : Composant, Catégorie, Valeur Actuelle, Valeur Originale, État
- **Boutons** : Rafraîchir, Randomiser, Restaurer, Tout Restaurer, Rapport
- **Coloration** : Gris (non modifiable), Jaune (modifié)

#### Logs & Activité
- **TextBox** en mode console avec timestamps
- **Couleurs** : Fond noir, texte vert (style terminal)
- **Scrolling automatique** vers le bas

#### Sauvegardes
- **ListView** des sauvegardes disponibles
- **Boutons** : Créer Sauvegarde, Restaurer Sauvegarde
- **Informations** : Date, Description, Nombre d'éléments

## 📊 Gestion des Événements

### Pattern Observer
```csharp
// Événements du gestionnaire HWID
public event EventHandler<string>? LogMessage;
public event EventHandler<HardwareOperationResult>? OperationCompleted;

// Abonnement dans l'interface
_hwidManager.LogMessage += OnLogMessage;
_hwidManager.OperationCompleted += OnOperationCompleted;
```

### Threading et Synchronisation
- **Invoke** pour les mises à jour UI depuis d'autres threads
- **Async/Await** pour les opérations longues
- **CancellationToken** pour l'annulation d'opérations

## 🔧 Configuration et Paramètres

### Fichiers de Configuration
- **app.manifest** : Privilèges administrateur et compatibilité
- **HwidSpoofer.csproj** : Configuration du projet .NET
- **Packages NuGet** : Dépendances externes

### Paramètres Runtime
```csharp
// Configuration de l'application
Application.EnableVisualStyles();
Application.SetCompatibleTextRenderingDefault(false);
Application.SetHighDpiMode(HighDpiMode.SystemAware);
```

## 🚀 Optimisations de Performance

### Détection Hardware
- **Cache** des résultats WMI
- **Lazy Loading** des informations non critiques
- **Timeout** sur les requêtes WMI longues

### Interface Utilisateur
- **Virtual Mode** pour les grandes listes
- **Double Buffering** pour réduire le scintillement
- **Mise à jour différée** des contrôles

### Mémoire
- **Dispose Pattern** pour les ressources WMI
- **Using Statements** pour la gestion automatique
- **Weak References** pour les événements

## 🐛 Gestion d'Erreurs

### Hiérarchie d'Exceptions
```csharp
// Résultat d'opération standardisé
public class HardwareOperationResult
{
    public bool Success { get; set; }
    public string Message { get; set; }
    public Exception? Exception { get; set; }
    public List<string> Warnings { get; set; }
}
```

### Stratégies de Récupération
- **Retry Logic** pour les échecs temporaires
- **Fallback Values** pour les détections échouées
- **Graceful Degradation** en cas d'erreur partielle
- **Logging détaillé** pour le débogage

## 📈 Métriques et Monitoring

### Logging
- **Timestamps** précis pour toutes les opérations
- **Niveaux** : Info, Warning, Error
- **Contexte** : Utilisateur, Machine, Version

### Performance
- **Temps de détection** hardware
- **Temps de modification** registre
- **Taille des sauvegardes**
- **Utilisation mémoire**

## 🔄 Cycle de Vie de l'Application

### Démarrage
1. **Vérification** des privilèges administrateur
2. **Affichage** des avertissements légaux
3. **Initialisation** du gestionnaire HWID
4. **Création** de la sauvegarde automatique
5. **Détection** du hardware actuel

### Fonctionnement
1. **Affichage** des informations détectées
2. **Interaction** utilisateur (sélection, modification)
3. **Validation** et application des changements
4. **Mise à jour** de l'interface
5. **Logging** des opérations

### Fermeture
1. **Vérification** des modifications en cours
2. **Proposition** de restauration
3. **Nettoyage** des ressources
4. **Sauvegarde** de l'état final

---

**Note** : Cette documentation technique est destinée aux développeurs souhaitant comprendre, modifier ou étendre le logiciel HWID Spoofer dans un cadre éducatif et légal.
