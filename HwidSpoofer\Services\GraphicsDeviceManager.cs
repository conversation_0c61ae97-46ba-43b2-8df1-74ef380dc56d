using System;
using System.Collections.Generic;
using System.Management;
using Microsoft.Win32;
using HwidSpoofer.Models;

namespace HwidSpoofer.Services
{
    /// <summary>
    /// Service de gestion des cartes graphiques et leurs identifiants
    /// </summary>
    public class GraphicsDeviceManager
    {
        private readonly RegistryManager _registryManager;
        private readonly List<GraphicsDevice> _detectedGpus = new();

        public GraphicsDeviceManager(RegistryManager registryManager)
        {
            _registryManager = registryManager;
        }

        /// <summary>
        /// Détecte toutes les cartes graphiques
        /// </summary>
        public List<GraphicsDevice> DetectGraphicsDevices()
        {
            _detectedGpus.Clear();

            try
            {
                DetectVideoControllers();
                DetectDisplayAdapters();
                EnrichWithRegistryInfo();
                
                return new List<GraphicsDevice>(_detectedGpus);
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur détection GPU: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Détecte via Win32_VideoController
        /// </summary>
        private void DetectVideoControllers()
        {
            using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController");
            
            foreach (ManagementObject gpu in searcher.Get())
            {
                var device = new GraphicsDevice
                {
                    DeviceId = gpu["DeviceID"]?.ToString() ?? "",
                    Name = gpu["Name"]?.ToString() ?? "Carte Graphique Inconnue",
                    Description = gpu["Description"]?.ToString() ?? "",
                    AdapterRam = Convert.ToInt64(gpu["AdapterRAM"] ?? 0),
                    DriverVersion = gpu["DriverVersion"]?.ToString() ?? "",
                    DriverDate = gpu["DriverDate"]?.ToString() ?? "",
                    VideoProcessor = gpu["VideoProcessor"]?.ToString() ?? "",
                    PnpDeviceId = gpu["PNPDeviceID"]?.ToString() ?? "",
                    Status = gpu["Status"]?.ToString() ?? "",
                    Availability = Convert.ToInt32(gpu["Availability"] ?? 0)
                };

                // Détecter le fabricant
                DetectManufacturer(device);
                
                // Détecter le type (intégré/dédié)
                DetectGpuType(device);
                
                _detectedGpus.Add(device);
            }
        }

        /// <summary>
        /// Détecte via Win32_DisplayConfiguration (Windows 10+)
        /// </summary>
        private void DetectDisplayAdapters()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PnPEntity WHERE Class='Display'");
                
                foreach (ManagementObject adapter in searcher.Get())
                {
                    var deviceId = adapter["DeviceID"]?.ToString();
                    var name = adapter["Name"]?.ToString();
                    
                    if (!string.IsNullOrEmpty(deviceId) && !string.IsNullOrEmpty(name))
                    {
                        // Vérifier si déjà détecté
                        var existing = _detectedGpus.Find(g => g.PnpDeviceId == deviceId);
                        if (existing == null)
                        {
                            var device = new GraphicsDevice
                            {
                                DeviceId = deviceId,
                                Name = name,
                                PnpDeviceId = deviceId,
                                Description = adapter["Description"]?.ToString() ?? ""
                            };

                            DetectManufacturer(device);
                            DetectGpuType(device);
                            _detectedGpus.Add(device);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur détection adaptateurs: {ex.Message}");
            }
        }

        /// <summary>
        /// Détermine le fabricant de la carte graphique
        /// </summary>
        private void DetectManufacturer(GraphicsDevice device)
        {
            var name = device.Name.ToUpper();
            var pnpId = device.PnpDeviceId.ToUpper();

            if (name.Contains("NVIDIA") || name.Contains("GEFORCE") || name.Contains("QUADRO") || pnpId.Contains("VEN_10DE"))
            {
                device.Manufacturer = "NVIDIA";
                device.VendorId = "10DE";
            }
            else if (name.Contains("AMD") || name.Contains("RADEON") || name.Contains("ATI") || pnpId.Contains("VEN_1002"))
            {
                device.Manufacturer = "AMD";
                device.VendorId = "1002";
            }
            else if (name.Contains("INTEL") || name.Contains("HD GRAPHICS") || name.Contains("UHD") || pnpId.Contains("VEN_8086"))
            {
                device.Manufacturer = "Intel";
                device.VendorId = "8086";
            }
            else
            {
                device.Manufacturer = "Inconnu";
                
                // Essayer d'extraire le VendorID du PnPDeviceID
                var match = System.Text.RegularExpressions.Regex.Match(pnpId, @"VEN_([0-9A-F]{4})");
                if (match.Success)
                {
                    device.VendorId = match.Groups[1].Value;
                }
            }
        }

        /// <summary>
        /// Détermine le type de GPU (intégré/dédié)
        /// </summary>
        private void DetectGpuType(GraphicsDevice device)
        {
            var name = device.Name.ToUpper();
            
            if (name.Contains("HD GRAPHICS") || name.Contains("UHD") || name.Contains("IRIS") ||
                name.Contains("VEGA") && name.Contains("GRAPHICS") ||
                device.AdapterRam < 1024 * 1024 * 1024) // Moins de 1GB
            {
                device.GpuType = "Intégré";
            }
            else
            {
                device.GpuType = "Dédié";
            }
        }

        /// <summary>
        /// Enrichit avec les informations du registre
        /// </summary>
        private void EnrichWithRegistryInfo()
        {
            const string videoPath = @"SYSTEM\CurrentControlSet\Control\Video";
            
            try
            {
                using var key = Registry.LocalMachine.OpenSubKey(videoPath);
                if (key == null) return;

                foreach (var gpu in _detectedGpus)
                {
                    FindGpuInRegistry(gpu, key, videoPath);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur enrichissement GPU registre: {ex.Message}");
            }
        }

        /// <summary>
        /// Cherche les informations GPU dans le registre
        /// </summary>
        private void FindGpuInRegistry(GraphicsDevice gpu, RegistryKey videoKey, string basePath)
        {
            try
            {
                foreach (string subKeyName in videoKey.GetSubKeyNames())
                {
                    using var subKey = videoKey.OpenSubKey(subKeyName);
                    if (subKey == null) continue;

                    // Chercher les sous-clés qui correspondent à notre GPU
                    foreach (string instanceName in subKey.GetSubKeyNames())
                    {
                        if (instanceName == "0000") // Instance principale
                        {
                            using var instanceKey = subKey.OpenSubKey(instanceName);
                            if (instanceKey == null) continue;

                            var deviceDesc = instanceKey.GetValue("Device Description")?.ToString();
                            var hardwareInfo = instanceKey.GetValue("HardwareInformation.AdapterString")?.ToString();
                            
                            if (!string.IsNullOrEmpty(deviceDesc) && 
                                (deviceDesc.Contains(gpu.Name) || gpu.Name.Contains(deviceDesc)))
                            {
                                gpu.RegistryPath = $"{basePath}\\{subKeyName}\\{instanceName}";
                                gpu.HardwareInformation = hardwareInfo ?? "";
                                
                                // Obtenir des informations supplémentaires
                                gpu.MemorySize = Convert.ToInt64(instanceKey.GetValue("HardwareInformation.MemorySize") ?? 0);
                                gpu.ChipType = instanceKey.GetValue("HardwareInformation.ChipType")?.ToString() ?? "";
                                gpu.BiosString = instanceKey.GetValue("HardwareInformation.BiosString")?.ToString() ?? "";
                                
                                break;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur recherche GPU registre: {ex.Message}");
            }
        }

        /// <summary>
        /// Modifie les identifiants d'une carte graphique
        /// </summary>
        public HardwareOperationResult ModifyGraphicsDevice(GraphicsDevice gpu, 
            string? newName = null, string? newVendorId = null, string? newDeviceId = null)
        {
            try
            {
                var modifications = new List<string>();

                if (!string.IsNullOrEmpty(gpu.RegistryPath))
                {
                    // Modifier le nom/description
                    if (!string.IsNullOrEmpty(newName))
                    {
                        var result = _registryManager.SetRegistryValue(
                            $"HKEY_LOCAL_MACHINE\\{gpu.RegistryPath}",
                            "Device Description",
                            newName
                        );
                        if (result.Success)
                        {
                            modifications.Add($"Name: {gpu.Name} -> {newName}");
                            gpu.Name = newName;
                        }

                        // Modifier aussi HardwareInformation.AdapterString
                        _registryManager.SetRegistryValue(
                            $"HKEY_LOCAL_MACHINE\\{gpu.RegistryPath}",
                            "HardwareInformation.AdapterString",
                            newName
                        );
                    }

                    // Modifier le Vendor ID
                    if (!string.IsNullOrEmpty(newVendorId))
                    {
                        var result = _registryManager.SetRegistryValue(
                            $"HKEY_LOCAL_MACHINE\\{gpu.RegistryPath}",
                            "VendorID",
                            newVendorId
                        );
                        if (result.Success)
                        {
                            modifications.Add($"VendorID: {gpu.VendorId} -> {newVendorId}");
                            gpu.VendorId = newVendorId;
                        }
                    }

                    // Modifier le Device ID
                    if (!string.IsNullOrEmpty(newDeviceId))
                    {
                        var result = _registryManager.SetRegistryValue(
                            $"HKEY_LOCAL_MACHINE\\{gpu.RegistryPath}",
                            "DeviceID",
                            newDeviceId
                        );
                        if (result.Success)
                        {
                            modifications.Add($"DeviceID: -> {newDeviceId}");
                        }
                    }
                }

                gpu.IsModified = modifications.Count > 0;
                if (gpu.IsModified)
                {
                    gpu.LastModified = DateTime.Now;
                }

                var message = modifications.Count > 0 
                    ? $"GPU modifié: {string.Join(", ", modifications)}"
                    : "Aucune modification appliquée";

                return HardwareOperationResult.CreateSuccess(message);
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur modification GPU: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Génère des identifiants GPU aléatoires
        /// </summary>
        public (string vendorId, string deviceId, string name) GenerateRandomGpuIds(string manufacturer = "")
        {
            var random = new Random();
            string vendorId, name;

            // Générer selon le fabricant
            switch (manufacturer.ToUpper())
            {
                case "NVIDIA":
                    vendorId = "10DE";
                    name = $"NVIDIA GeForce GTX {random.Next(1000, 4090)}";
                    break;
                case "AMD":
                    vendorId = "1002";
                    name = $"AMD Radeon RX {random.Next(5000, 7000)}";
                    break;
                case "INTEL":
                    vendorId = "8086";
                    name = $"Intel UHD Graphics {random.Next(600, 800)}";
                    break;
                default:
                    vendorId = random.Next(0x1000, 0xFFFF).ToString("X4");
                    name = $"Generic Graphics Card {random.Next(1000, 9999)}";
                    break;
            }

            var deviceId = random.Next(0x1000, 0xFFFF).ToString("X4");

            return (vendorId, deviceId, name);
        }

        /// <summary>
        /// Restaure les identifiants originaux d'un GPU
        /// </summary>
        public HardwareOperationResult RestoreGraphicsDevice(GraphicsDevice gpu)
        {
            try
            {
                if (string.IsNullOrEmpty(gpu.RegistryPath))
                {
                    return HardwareOperationResult.CreateError("Chemin de registre non disponible");
                }

                var result = _registryManager.RestoreRegistryValue(gpu.RegistryPath, "Device Description");
                if (!result.Success) return result;

                result = _registryManager.RestoreRegistryValue(gpu.RegistryPath, "VendorID");
                if (!result.Success) return result;

                result = _registryManager.RestoreRegistryValue(gpu.RegistryPath, "DeviceID");
                if (!result.Success) return result;

                gpu.IsModified = false;
                gpu.LastModified = DateTime.MinValue;

                return HardwareOperationResult.CreateSuccess($"GPU restauré: {gpu.Name}");
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur restauration GPU: {ex.Message}", ex);
            }
        }
    }

    /// <summary>
    /// Modèle représentant une carte graphique
    /// </summary>
    public class GraphicsDevice
    {
        public string DeviceId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Manufacturer { get; set; } = string.Empty;
        public string VendorId { get; set; } = string.Empty;
        public string GpuType { get; set; } = string.Empty;  // Intégré/Dédié
        public string VideoProcessor { get; set; } = string.Empty;
        public string DriverVersion { get; set; } = string.Empty;
        public string DriverDate { get; set; } = string.Empty;
        public string PnpDeviceId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string RegistryPath { get; set; } = string.Empty;
        public string HardwareInformation { get; set; } = string.Empty;
        public string ChipType { get; set; } = string.Empty;
        public string BiosString { get; set; } = string.Empty;
        public long AdapterRam { get; set; }
        public long MemorySize { get; set; }
        public int Availability { get; set; }
        public bool IsModified { get; set; } = false;
        public DateTime LastModified { get; set; } = DateTime.MinValue;

        public string DisplayName => $"{Name} ({Manufacturer}) - {FormatMemory(AdapterRam)}";
        public string MemoryFormatted => FormatMemory(AdapterRam);

        private string FormatMemory(long bytes)
        {
            if (bytes == 0) return "Mémoire inconnue";
            
            var gb = bytes / (1024.0 * 1024.0 * 1024.0);
            if (gb >= 1)
                return $"{gb:0.#} GB";
            
            var mb = bytes / (1024.0 * 1024.0);
            return $"{mb:0} MB";
        }

        public override string ToString() => DisplayName;
    }
}
