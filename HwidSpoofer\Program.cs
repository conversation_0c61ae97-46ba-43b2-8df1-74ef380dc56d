using System;
using System.Diagnostics;
using System.Security.Principal;
using System.Windows.Forms;

namespace HwidSpoofer
{
    internal static class Program
    {
        /// <summary>
        /// Point d'entrée principal de l'application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // Vérifier si l'application est lancée en tant qu'administrateur
            if (!IsRunningAsAdministrator())
            {
                MessageBox.Show(
                    "Cette application nécessite des privilèges administrateur pour fonctionner correctement.\n\n" +
                    "Veuillez relancer l'application en tant qu'administrateur.",
                    "Privilèges Administrateur Requis",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );

                // Tenter de relancer en tant qu'administrateur
                try
                {
                    ProcessStartInfo startInfo = new ProcessStartInfo
                    {
                        UseShellExecute = true,
                        WorkingDirectory = Environment.CurrentDirectory,
                        FileName = Application.ExecutablePath,
                        Verb = "runas"
                    };

                    Process.Start(startInfo);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        $"Impossible de relancer l'application en tant qu'administrateur:\n{ex.Message}",
                        "Erreur",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error
                    );
                }

                return;
            }

            // Afficher l'avertissement légal au démarrage
            DialogResult result = MessageBox.Show(
                "⚠️ AVERTISSEMENT IMPORTANT ⚠️\n\n" +
                "Ce logiciel est destiné UNIQUEMENT à des fins éducatives et de recherche.\n\n" +
                "❌ N'utilisez PAS ce logiciel pour :\n" +
                "   • Contourner des bans ou restrictions\n" +
                "   • Violer des conditions d'utilisation\n" +
                "   • Toute activité illégale ou malveillante\n\n" +
                "✅ Usage autorisé :\n" +
                "   • Tests sur vos propres systèmes\n" +
                "   • Recherche académique\n" +
                "   • Apprentissage de la sécurité informatique\n\n" +
                "L'auteur n'est PAS responsable de l'usage malveillant de cet outil.\n\n" +
                "Acceptez-vous ces conditions d'utilisation ?",
                "Conditions d'Utilisation - HWID Spoofer",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning,
                MessageBoxDefaultButton.Button2
            );

            if (result != DialogResult.Yes)
            {
                MessageBox.Show(
                    "Vous devez accepter les conditions d'utilisation pour continuer.",
                    "Conditions Refusées",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
                return;
            }

            // Configuration de l'application
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                // Lancer l'interface principale
                Application.Run(new MainForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Une erreur critique s'est produite :\n\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}",
                    "Erreur Critique",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
        }

        /// <summary>
        /// Vérifie si l'application est lancée avec des privilèges administrateur
        /// </summary>
        /// <returns>True si administrateur, False sinon</returns>
        private static bool IsRunningAsAdministrator()
        {
            try
            {
                WindowsIdentity identity = WindowsIdentity.GetCurrent();
                WindowsPrincipal principal = new WindowsPrincipal(identity);
                return principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }
    }
}
