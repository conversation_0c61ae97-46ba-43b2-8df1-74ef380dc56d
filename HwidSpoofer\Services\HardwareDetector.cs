using System;
using System.Collections.Generic;
using System.Management;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Win32;
using HwidSpoofer.Models;

namespace HwidSpoofer.Services
{
    /// <summary>
    /// Service de détection des informations hardware du système
    /// </summary>
    public class HardwareDetector
    {
        private readonly Dictionary<string, HardwareInfo> _detectedHardware = new();

        /// <summary>
        /// Détecte toutes les informations hardware du système
        /// </summary>
        public Dictionary<string, HardwareInfo> DetectAllHardware()
        {
            _detectedHardware.Clear();

            try
            {
                DetectProcessorInfo();
                DetectMotherboardInfo();
                DetectStorageInfo();
                DetectNetworkInfo();
                DetectBiosInfo();
                DetectSystemInfo();
                DetectMemoryInfo();
                DetectGraphicsInfo();
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de la détection hardware: {ex.Message}", ex);
            }

            return new Dictionary<string, HardwareInfo>(_detectedHardware);
        }

        /// <summary>
        /// Détecte les informations du processeur
        /// </summary>
        private void DetectProcessorInfo()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor");
                foreach (ManagementObject obj in searcher.Get())
                {
                    var processorId = obj["ProcessorId"]?.ToString() ?? "N/A";
                    var name = obj["Name"]?.ToString() ?? "Processeur Inconnu";

                    _detectedHardware["CPU_ID"] = new HardwareInfo(
                        "ID Processeur",
                        "Processor",
                        processorId,
                        @"HKEY_LOCAL_MACHINE\HARDWARE\DESCRIPTION\System\CentralProcessor\0",
                        "Identifier"
                    );

                    _detectedHardware["CPU_NAME"] = new HardwareInfo(
                        "Nom Processeur",
                        "Processor",
                        name,
                        @"HKEY_LOCAL_MACHINE\HARDWARE\DESCRIPTION\System\CentralProcessor\0",
                        "ProcessorNameString"
                    );

                    break; // Premier processeur seulement
                }
            }
            catch (Exception ex)
            {
                AddErrorInfo("CPU_ERROR", "Erreur Processeur", ex.Message);
            }
        }

        /// <summary>
        /// Détecte les informations de la carte mère
        /// </summary>
        private void DetectMotherboardInfo()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_BaseBoard");
                foreach (ManagementObject obj in searcher.Get())
                {
                    var serialNumber = obj["SerialNumber"]?.ToString() ?? "N/A";
                    var product = obj["Product"]?.ToString() ?? "Carte Mère Inconnue";

                    _detectedHardware["MOTHERBOARD_SERIAL"] = new HardwareInfo(
                        "Numéro de Série Carte Mère",
                        "Motherboard",
                        serialNumber,
                        @"HKEY_LOCAL_MACHINE\SYSTEM\HardwareConfig",
                        "LastConfig"
                    );

                    _detectedHardware["MOTHERBOARD_PRODUCT"] = new HardwareInfo(
                        "Modèle Carte Mère",
                        "Motherboard",
                        product,
                        @"HKEY_LOCAL_MACHINE\HARDWARE\DESCRIPTION\System\BIOS",
                        "BaseBoardProduct"
                    );

                    break;
                }
            }
            catch (Exception ex)
            {
                AddErrorInfo("MOTHERBOARD_ERROR", "Erreur Carte Mère", ex.Message);
            }
        }

        /// <summary>
        /// Détecte les informations de stockage
        /// </summary>
        private void DetectStorageInfo()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_DiskDrive");
                int diskIndex = 0;
                foreach (ManagementObject obj in searcher.Get())
                {
                    var serialNumber = obj["SerialNumber"]?.ToString()?.Trim() ?? "N/A";
                    var model = obj["Model"]?.ToString() ?? "Disque Inconnu";

                    _detectedHardware[$"DISK_{diskIndex}_SERIAL"] = new HardwareInfo(
                        $"Numéro de Série Disque {diskIndex}",
                        "Storage",
                        serialNumber,
                        @"HKEY_LOCAL_MACHINE\HARDWARE\DEVICEMAP\Scsi",
                        $"Scsi Port {diskIndex}"
                    );

                    _detectedHardware[$"DISK_{diskIndex}_MODEL"] = new HardwareInfo(
                        $"Modèle Disque {diskIndex}",
                        "Storage",
                        model,
                        @"HKEY_LOCAL_MACHINE\HARDWARE\DEVICEMAP\Scsi",
                        $"Scsi Port {diskIndex}"
                    );

                    diskIndex++;
                    if (diskIndex >= 3) break; // Limiter à 3 disques
                }
            }
            catch (Exception ex)
            {
                AddErrorInfo("STORAGE_ERROR", "Erreur Stockage", ex.Message);
            }
        }

        /// <summary>
        /// Détecte les informations réseau
        /// </summary>
        private void DetectNetworkInfo()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_NetworkAdapter WHERE PhysicalAdapter=True AND MACAddress IS NOT NULL");
                int adapterIndex = 0;
                foreach (ManagementObject obj in searcher.Get())
                {
                    var macAddress = obj["MACAddress"]?.ToString() ?? "N/A";
                    var name = obj["Name"]?.ToString() ?? "Adaptateur Inconnu";

                    _detectedHardware[$"NETWORK_{adapterIndex}_MAC"] = new HardwareInfo(
                        $"Adresse MAC {adapterIndex}",
                        "Network",
                        macAddress,
                        @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}",
                        "NetworkAddress"
                    );

                    adapterIndex++;
                    if (adapterIndex >= 2) break; // Limiter à 2 adaptateurs
                }
            }
            catch (Exception ex)
            {
                AddErrorInfo("NETWORK_ERROR", "Erreur Réseau", ex.Message);
            }
        }

        /// <summary>
        /// Détecte les informations BIOS
        /// </summary>
        private void DetectBiosInfo()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_BIOS");
                foreach (ManagementObject obj in searcher.Get())
                {
                    var serialNumber = obj["SerialNumber"]?.ToString() ?? "N/A";
                    var version = obj["SMBIOSBIOSVersion"]?.ToString() ?? "N/A";

                    _detectedHardware["BIOS_SERIAL"] = new HardwareInfo(
                        "Numéro de Série BIOS",
                        "BIOS",
                        serialNumber,
                        @"HKEY_LOCAL_MACHINE\HARDWARE\DESCRIPTION\System\BIOS",
                        "BIOSSerialNumber"
                    );

                    _detectedHardware["BIOS_VERSION"] = new HardwareInfo(
                        "Version BIOS",
                        "BIOS",
                        version,
                        @"HKEY_LOCAL_MACHINE\HARDWARE\DESCRIPTION\System\BIOS",
                        "BIOSVersion"
                    );

                    break;
                }
            }
            catch (Exception ex)
            {
                AddErrorInfo("BIOS_ERROR", "Erreur BIOS", ex.Message);
            }
        }

        /// <summary>
        /// Détecte les informations système
        /// </summary>
        private void DetectSystemInfo()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystemProduct");
                foreach (ManagementObject obj in searcher.Get())
                {
                    var uuid = obj["UUID"]?.ToString() ?? "N/A";

                    _detectedHardware["SYSTEM_UUID"] = new HardwareInfo(
                        "UUID Système",
                        "System",
                        uuid,
                        @"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography",
                        "MachineGuid"
                    );

                    break;
                }

                // Machine GUID depuis le registre
                try
                {
                    using var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Cryptography");
                    var machineGuid = key?.GetValue("MachineGuid")?.ToString() ?? "N/A";

                    _detectedHardware["MACHINE_GUID"] = new HardwareInfo(
                        "GUID Machine",
                        "System",
                        machineGuid,
                        @"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography",
                        "MachineGuid"
                    );
                }
                catch (Exception ex)
                {
                    AddErrorInfo("MACHINE_GUID_ERROR", "Erreur GUID Machine", ex.Message);
                }
            }
            catch (Exception ex)
            {
                AddErrorInfo("SYSTEM_ERROR", "Erreur Système", ex.Message);
            }
        }

        /// <summary>
        /// Détecte les informations mémoire
        /// </summary>
        private void DetectMemoryInfo()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMemory");
                int memoryIndex = 0;
                foreach (ManagementObject obj in searcher.Get())
                {
                    var serialNumber = obj["SerialNumber"]?.ToString()?.Trim() ?? "N/A";
                    var partNumber = obj["PartNumber"]?.ToString()?.Trim() ?? "N/A";

                    if (!string.IsNullOrEmpty(serialNumber) && serialNumber != "N/A")
                    {
                        _detectedHardware[$"MEMORY_{memoryIndex}_SERIAL"] = new HardwareInfo(
                            $"Numéro de Série Mémoire {memoryIndex}",
                            "Memory",
                            serialNumber,
                            @"HKEY_LOCAL_MACHINE\HARDWARE\DESCRIPTION\System\MultifunctionAdapter",
                            $"Memory{memoryIndex}"
                        );
                    }

                    memoryIndex++;
                    if (memoryIndex >= 4) break; // Limiter à 4 modules
                }
            }
            catch (Exception ex)
            {
                AddErrorInfo("MEMORY_ERROR", "Erreur Mémoire", ex.Message);
            }
        }

        /// <summary>
        /// Détecte les informations graphiques
        /// </summary>
        private void DetectGraphicsInfo()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController");
                int gpuIndex = 0;
                foreach (ManagementObject obj in searcher.Get())
                {
                    var name = obj["Name"]?.ToString() ?? "Carte Graphique Inconnue";
                    var pnpDeviceId = obj["PNPDeviceID"]?.ToString() ?? "N/A";

                    _detectedHardware[$"GPU_{gpuIndex}_NAME"] = new HardwareInfo(
                        $"Nom Carte Graphique {gpuIndex}",
                        "Graphics",
                        name,
                        @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Video",
                        "Device Description"
                    );

                    _detectedHardware[$"GPU_{gpuIndex}_ID"] = new HardwareInfo(
                        $"ID Carte Graphique {gpuIndex}",
                        "Graphics",
                        pnpDeviceId,
                        @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\PCI",
                        "DeviceID"
                    );

                    gpuIndex++;
                    if (gpuIndex >= 2) break; // Limiter à 2 cartes graphiques
                }
            }
            catch (Exception ex)
            {
                AddErrorInfo("GRAPHICS_ERROR", "Erreur Graphiques", ex.Message);
            }
        }

        /// <summary>
        /// Ajoute une information d'erreur à la liste des hardware détectés
        /// </summary>
        private void AddErrorInfo(string key, string name, string error)
        {
            _detectedHardware[key] = new HardwareInfo(
                name,
                "Error",
                $"Erreur: {error}",
                "",
                ""
            )
            {
                CanBeModified = false
            };
        }

        /// <summary>
        /// Génère un identifiant hardware unique basé sur plusieurs composants
        /// </summary>
        public string GenerateHardwareFingerprint()
        {
            var hardware = DetectAllHardware();
            var fingerprint = new StringBuilder();

            // Combiner les identifiants les plus stables
            var keys = new[] { "CPU_ID", "MOTHERBOARD_SERIAL", "BIOS_SERIAL", "SYSTEM_UUID" };

            foreach (var key in keys)
            {
                if (hardware.ContainsKey(key))
                {
                    fingerprint.Append(hardware[key].CurrentValue);
                }
            }

            // Générer un hash SHA256
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(fingerprint.ToString()));
            return Convert.ToHexString(hash);
        }

        /// <summary>
        /// Génère une nouvelle valeur aléatoire pour un type d'identifiant donné
        /// </summary>
        public string GenerateRandomValue(string hardwareType)
        {
            var random = new Random();

            return hardwareType.ToUpper() switch
            {
                "CPU_ID" => GenerateRandomHex(16),
                "MOTHERBOARD_SERIAL" => $"MB{GenerateRandomAlphaNumeric(8)}",
                "BIOS_SERIAL" => $"BIOS{GenerateRandomAlphaNumeric(6)}",
                "SYSTEM_UUID" => GenerateRandomGuid(),
                "MACHINE_GUID" => GenerateRandomGuid(),
                var key when key.Contains("MAC") => GenerateRandomMacAddress(),
                var key when key.Contains("SERIAL") => GenerateRandomAlphaNumeric(10),
                _ => GenerateRandomAlphaNumeric(8)
            };
        }

        private string GenerateRandomHex(int length)
        {
            var random = new Random();
            var hex = new StringBuilder();
            for (int i = 0; i < length; i++)
            {
                hex.Append(random.Next(0, 16).ToString("X"));
            }
            return hex.ToString();
        }

        private string GenerateRandomAlphaNumeric(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var random = new Random();
            var result = new StringBuilder();
            for (int i = 0; i < length; i++)
            {
                result.Append(chars[random.Next(chars.Length)]);
            }
            return result.ToString();
        }

        private string GenerateRandomGuid()
        {
            return Guid.NewGuid().ToString().ToUpper();
        }

        private string GenerateRandomMacAddress()
        {
            var random = new Random();
            var mac = new byte[6];
            random.NextBytes(mac);

            // S'assurer que c'est une adresse MAC valide (bit local admin à 0)
            mac[0] = (byte)(mac[0] & 0xFE);

            return string.Join(":", mac.Select(b => b.ToString("X2")));
        }
    }
}
