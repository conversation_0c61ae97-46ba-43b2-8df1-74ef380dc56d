@echo off
setlocal enabledelayedexpansion

:: ========================================
::    HWID CHECK - Verification Complete
:: ========================================
:: Affiche tous les identifiants actuels
:: ========================================

title HWID Check - Verification des Identifiants

:: Couleurs
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "RESET=[0m"

echo %CYAN%========================================%RESET%
echo %CYAN%    HWID CHECK - Identifiants Actuels%RESET%
echo %CYAN%========================================%RESET%
echo.

:: ========================================
:: 1. INFORMATIONS SYSTEME
:: ========================================
echo %BLUE%=== INFORMATIONS SYSTEME ===%RESET%
echo.

:: Machine GUID
echo %YELLOW%Machine GUID:%RESET%
for /f "tokens=3" %%a in ('reg query "HKLM\SOFTWARE\Microsoft\Cryptography" /v MachineGuid 2^>nul') do (
    echo %WHITE%  %%a%RESET%
)

:: Nom d'ordinateur
echo.
echo %YELLOW%Nom d'ordinateur:%RESET%
for /f "tokens=3" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName" /v ComputerName 2^>nul') do (
    echo %WHITE%  %%a%RESET%
)

:: Product ID
echo.
echo %YELLOW%Product ID Windows:%RESET%
for /f "tokens=3" %%a in ('reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion" /v ProductId 2^>nul') do (
    echo %WHITE%  %%a%RESET%
)

:: Propriétaire enregistré
echo.
echo %YELLOW%Proprietaire enregistre:%RESET%
for /f "tokens=3*" %%a in ('reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion" /v RegisteredOwner 2^>nul') do (
    echo %WHITE%  %%a %%b%RESET%
)

:: ========================================
:: 2. INFORMATIONS HARDWARE VIA WMI
:: ========================================
echo.
echo %BLUE%=== INFORMATIONS HARDWARE (WMI) ===%RESET%
echo.

:: Processeur
echo %YELLOW%Processeur:%RESET%
for /f "skip=1 tokens=*" %%a in ('wmic cpu get ProcessorId /format:list 2^>nul ^| findstr "="') do (
    echo %WHITE%  %%a%RESET%
)

:: Carte mère
echo.
echo %YELLOW%Carte mere:%RESET%
for /f "skip=1 tokens=*" %%a in ('wmic baseboard get SerialNumber /format:list 2^>nul ^| findstr "="') do (
    echo %WHITE%  %%a%RESET%
)

:: BIOS
echo.
echo %YELLOW%BIOS:%RESET%
for /f "skip=1 tokens=*" %%a in ('wmic bios get SerialNumber /format:list 2^>nul ^| findstr "="') do (
    echo %WHITE%  %%a%RESET%
)

:: ========================================
:: 3. ADRESSES MAC
:: ========================================
echo.
echo %BLUE%=== ADRESSES MAC ===%RESET%
echo.

set "MAC_COUNT=0"
for /f "skip=1 tokens=*" %%a in ('wmic path Win32_NetworkAdapter where "PhysicalAdapter=True and MACAddress is not null" get Name^,MACAddress /format:csv 2^>nul') do (
    set "LINE=%%a"
    if not "!LINE!"=="" (
        for /f "tokens=2,3 delims=," %%b in ("!LINE!") do (
            if not "%%c"=="" (
                echo %YELLOW%  %%b:%RESET% %WHITE%%%c%RESET%
                set /a "MAC_COUNT+=1"
            )
        )
    )
)

if %MAC_COUNT% equ 0 (
    echo %RED%  Aucune adresse MAC trouvee%RESET%
)

:: ========================================
:: 4. DISQUES
:: ========================================
echo.
echo %BLUE%=== DISQUES ===%RESET%
echo.

set "DISK_COUNT=0"
for /f "skip=1 tokens=*" %%a in ('wmic diskdrive get Model^,SerialNumber /format:csv 2^>nul') do (
    set "LINE=%%a"
    if not "!LINE!"=="" (
        for /f "tokens=2,3 delims=," %%b in ("!LINE!") do (
            if not "%%c"=="" (
                echo %YELLOW%  %%b:%RESET% %WHITE%%%c%RESET%
                set /a "DISK_COUNT+=1"
            )
        )
    )
)

if %DISK_COUNT% equ 0 (
    echo %RED%  Aucun disque trouve%RESET%
)

:: ========================================
:: 5. PERIPHERIQUES USB
:: ========================================
echo.
echo %BLUE%=== PERIPHERIQUES USB ===%RESET%
echo.

set "USB_COUNT=0"
for /f "skip=1 tokens=*" %%a in ('wmic path Win32_PnPEntity where "DeviceID like 'USB%%'" get Name^,DeviceID /format:csv 2^>nul') do (
    set "LINE=%%a"
    if not "!LINE!"=="" (
        for /f "tokens=2,3 delims=," %%b in ("!LINE!") do (
            if not "%%c"=="" (
                echo %YELLOW%  %%c%RESET%
                echo %WHITE%    ID: %%b%RESET%
                set /a "USB_COUNT+=1"
                if !USB_COUNT! geq 5 goto :EndUSBList
            )
        )
    )
)
:EndUSBList

if %USB_COUNT% equ 0 (
    echo %RED%  Aucun peripherique USB trouve%RESET%
)

:: ========================================
:: 6. EMPREINTE HARDWARE
:: ========================================
echo.
echo %BLUE%=== EMPREINTE HARDWARE ===%RESET%
echo.

:: Générer une empreinte basée sur les identifiants principaux
set "FINGERPRINT_DATA="

:: Ajouter Machine GUID
for /f "tokens=3" %%a in ('reg query "HKLM\SOFTWARE\Microsoft\Cryptography" /v MachineGuid 2^>nul') do (
    set "FINGERPRINT_DATA=!FINGERPRINT_DATA!%%a"
)

:: Ajouter nom d'ordinateur
for /f "tokens=3" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName" /v ComputerName 2^>nul') do (
    set "FINGERPRINT_DATA=!FINGERPRINT_DATA!%%a"
)

:: Ajouter Product ID
for /f "tokens=3" %%a in ('reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion" /v ProductId 2^>nul') do (
    set "FINGERPRINT_DATA=!FINGERPRINT_DATA!%%a"
)

:: Calculer un hash simple (somme des codes ASCII)
set "HASH=0"
set "STR=!FINGERPRINT_DATA!"
:HashLoop
if "!STR!"=="" goto :EndHash
set "CHAR=!STR:~0,1!"
set "STR=!STR:~1!"
for /f %%a in ('powershell -Command "[int][char]'!CHAR!'"') do (
    set /a "HASH+=%%a"
)
goto :HashLoop
:EndHash

echo %YELLOW%Empreinte Hardware (Hash):%RESET% %WHITE%!HASH!%RESET%

:: ========================================
:: 7. INFORMATIONS COMPLEMENTAIRES
:: ========================================
echo.
echo %BLUE%=== INFORMATIONS COMPLEMENTAIRES ===%RESET%
echo.

:: Date et heure
echo %YELLOW%Date/Heure:%RESET% %WHITE%!DATE! !TIME!%RESET%

:: Version Windows
for /f "tokens=3*" %%a in ('reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion" /v ProductName 2^>nul') do (
    echo %YELLOW%Version Windows:%RESET% %WHITE%%%a %%b%RESET%
)

:: Architecture
echo %YELLOW%Architecture:%RESET% %WHITE%!PROCESSOR_ARCHITECTURE!%RESET%

:: Utilisateur actuel
echo %YELLOW%Utilisateur:%RESET% %WHITE%!USERNAME!%RESET%

:: ========================================
:: 8. SAUVEGARDER LE RAPPORT
:: ========================================
echo.
echo %BLUE%=== SAUVEGARDE DU RAPPORT ===%RESET%
echo.

set "REPORT_FILE=%USERPROFILE%\Desktop\HWID_Report_%DATE:~-4%%DATE:~3,2%%DATE:~0,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%.txt"
set "REPORT_FILE=%REPORT_FILE: =%"

echo Rapport HWID genere le !DATE! a !TIME! > "!REPORT_FILE!"
echo. >> "!REPORT_FILE!"
echo === IDENTIFIANTS SYSTEME === >> "!REPORT_FILE!"

:: Machine GUID
for /f "tokens=3" %%a in ('reg query "HKLM\SOFTWARE\Microsoft\Cryptography" /v MachineGuid 2^>nul') do (
    echo Machine GUID: %%a >> "!REPORT_FILE!"
)

:: Nom d'ordinateur
for /f "tokens=3" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName" /v ComputerName 2^>nul') do (
    echo Nom ordinateur: %%a >> "!REPORT_FILE!"
)

:: Product ID
for /f "tokens=3" %%a in ('reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion" /v ProductId 2^>nul') do (
    echo Product ID: %%a >> "!REPORT_FILE!"
)

echo. >> "!REPORT_FILE!"
echo === EMPREINTE HARDWARE === >> "!REPORT_FILE!"
echo Hash: !HASH! >> "!REPORT_FILE!"

echo %GREEN%✓ Rapport sauvegarde: !REPORT_FILE!%RESET%

:: ========================================
:: FINALISATION
:: ========================================
echo.
echo %CYAN%========================================%RESET%
echo %CYAN%        VERIFICATION TERMINEE%RESET%
echo %CYAN%========================================%RESET%
echo.
echo %GREEN%✓ Tous les identifiants ont ete affiches%RESET%
echo %GREEN%✓ Rapport sauvegarde sur le bureau%RESET%
echo.

pause
