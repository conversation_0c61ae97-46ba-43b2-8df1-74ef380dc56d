using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using HwidSpoofer.Models;

namespace HwidSpoofer.Services
{
    /// <summary>
    /// Gestionnaire de profils de spoofing prédéfinis
    /// </summary>
    public class ProfileManager
    {
        private readonly string _profilesDirectory;
        private readonly JsonSerializerOptions _jsonOptions;

        public ProfileManager()
        {
            _profilesDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "HwidSpoofer",
                "Profiles"
            );

            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            Directory.CreateDirectory(_profilesDirectory);
            CreateDefaultProfiles();
        }

        /// <summary>
        /// Crée les profils par défaut
        /// </summary>
        private void CreateDefaultProfiles()
        {
            var defaultProfiles = new[]
            {
                CreateGamingProfile(),
                CreateWorkstationProfile(),
                CreateLaptopProfile(),
                CreateServerProfile(),
                CreateTestingProfile()
            };

            foreach (var profile in defaultProfiles)
            {
                var filePath = Path.Combine(_profilesDirectory, $"{profile.Name}.json");
                if (!File.Exists(filePath))
                {
                    SaveProfile(profile);
                }
            }
        }

        /// <summary>
        /// Crée un profil Gaming typique
        /// </summary>
        private SpoofingProfile CreateGamingProfile()
        {
            return new SpoofingProfile
            {
                Name = "Gaming PC",
                Description = "Configuration typique d'un PC gaming",
                Category = "Gaming",
                CreatedAt = DateTime.Now,
                HardwareSettings = new Dictionary<string, ProfileSetting>
                {
                    ["CPU_ID"] = new ProfileSetting { SpoofType = SpoofType.Random, Value = "" },
                    ["MOTHERBOARD_SERIAL"] = new ProfileSetting { SpoofType = SpoofType.Random, Value = "" },
                    ["GPU_NAME"] = new ProfileSetting { SpoofType = SpoofType.Custom, Value = "NVIDIA GeForce RTX 4080" },
                    ["GPU_VENDOR"] = new ProfileSetting { SpoofType = SpoofType.Custom, Value = "10DE" },
                    ["DISK_0_SERIAL"] = new ProfileSetting { SpoofType = SpoofType.Random, Value = "" },
                    ["NETWORK_0_MAC"] = new ProfileSetting { SpoofType = SpoofType.Random, Value = "" },
                    ["SYSTEM_UUID"] = new ProfileSetting { SpoofType = SpoofType.Random, Value = "" }
                },
                UsbSettings = new Dictionary<string, UsbProfileSetting>
                {
                    ["Gaming_Mouse"] = new UsbProfileSetting 
                    { 
                        VendorId = "046D", 
                        ProductId = "C52B", 
                        Name = "Logitech Gaming Mouse" 
                    },
                    ["Gaming_Keyboard"] = new UsbProfileSetting 
                    { 
                        VendorId = "1B1C", 
                        ProductId = "1B15", 
                        Name = "Corsair Gaming Keyboard" 
                    }
                }
            };
        }

        /// <summary>
        /// Crée un profil Workstation
        /// </summary>
        private SpoofingProfile CreateWorkstationProfile()
        {
            return new SpoofingProfile
            {
                Name = "Workstation Pro",
                Description = "Configuration professionnelle",
                Category = "Professional",
                CreatedAt = DateTime.Now,
                HardwareSettings = new Dictionary<string, ProfileSetting>
                {
                    ["CPU_ID"] = new ProfileSetting { SpoofType = SpoofType.Custom, Value = "Intel Xeon" },
                    ["GPU_NAME"] = new ProfileSetting { SpoofType = SpoofType.Custom, Value = "NVIDIA Quadro RTX 4000" },
                    ["GPU_VENDOR"] = new ProfileSetting { SpoofType = SpoofType.Custom, Value = "10DE" },
                    ["MOTHERBOARD_SERIAL"] = new ProfileSetting { SpoofType = SpoofType.Random, Value = "" }
                }
            };
        }

        /// <summary>
        /// Crée un profil Laptop
        /// </summary>
        private SpoofingProfile CreateLaptopProfile()
        {
            return new SpoofingProfile
            {
                Name = "Laptop Standard",
                Description = "Configuration laptop typique",
                Category = "Mobile",
                CreatedAt = DateTime.Now,
                HardwareSettings = new Dictionary<string, ProfileSetting>
                {
                    ["GPU_NAME"] = new ProfileSetting { SpoofType = SpoofType.Custom, Value = "Intel UHD Graphics 620" },
                    ["GPU_VENDOR"] = new ProfileSetting { SpoofType = SpoofType.Custom, Value = "8086" },
                    ["DISK_0_SERIAL"] = new ProfileSetting { SpoofType = SpoofType.Random, Value = "" }
                }
            };
        }

        /// <summary>
        /// Crée un profil Server
        /// </summary>
        private SpoofingProfile CreateServerProfile()
        {
            return new SpoofingProfile
            {
                Name = "Server Enterprise",
                Description = "Configuration serveur d'entreprise",
                Category = "Server",
                CreatedAt = DateTime.Now,
                HardwareSettings = new Dictionary<string, ProfileSetting>
                {
                    ["CPU_ID"] = new ProfileSetting { SpoofType = SpoofType.Custom, Value = "Intel Xeon Gold" },
                    ["MOTHERBOARD_SERIAL"] = new ProfileSetting { SpoofType = SpoofType.Custom, Value = "DELL-SERVER" }
                }
            };
        }

        /// <summary>
        /// Crée un profil de test
        /// </summary>
        private SpoofingProfile CreateTestingProfile()
        {
            return new SpoofingProfile
            {
                Name = "Testing Environment",
                Description = "Profil pour tests et développement",
                Category = "Testing",
                CreatedAt = DateTime.Now,
                HardwareSettings = new Dictionary<string, ProfileSetting>
                {
                    ["CPU_ID"] = new ProfileSetting { SpoofType = SpoofType.Random, Value = "" },
                    ["MOTHERBOARD_SERIAL"] = new ProfileSetting { SpoofType = SpoofType.Random, Value = "" },
                    ["GPU_NAME"] = new ProfileSetting { SpoofType = SpoofType.Random, Value = "" },
                    ["DISK_0_SERIAL"] = new ProfileSetting { SpoofType = SpoofType.Random, Value = "" },
                    ["SYSTEM_UUID"] = new ProfileSetting { SpoofType = SpoofType.Random, Value = "" }
                }
            };
        }

        /// <summary>
        /// Sauvegarde un profil
        /// </summary>
        public HardwareOperationResult SaveProfile(SpoofingProfile profile)
        {
            try
            {
                var fileName = $"{SanitizeFileName(profile.Name)}.json";
                var filePath = Path.Combine(_profilesDirectory, fileName);

                var json = JsonSerializer.Serialize(profile, _jsonOptions);
                File.WriteAllText(filePath, json);

                return HardwareOperationResult.CreateSuccess($"Profil sauvegardé: {profile.Name}");
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur sauvegarde profil: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Charge un profil
        /// </summary>
        public SpoofingProfile? LoadProfile(string profileName)
        {
            try
            {
                var fileName = $"{SanitizeFileName(profileName)}.json";
                var filePath = Path.Combine(_profilesDirectory, fileName);

                if (!File.Exists(filePath))
                    return null;

                var json = File.ReadAllText(filePath);
                return JsonSerializer.Deserialize<SpoofingProfile>(json, _jsonOptions);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur chargement profil {profileName}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Obtient la liste de tous les profils
        /// </summary>
        public List<SpoofingProfile> GetAllProfiles()
        {
            var profiles = new List<SpoofingProfile>();

            try
            {
                var profileFiles = Directory.GetFiles(_profilesDirectory, "*.json");

                foreach (var file in profileFiles)
                {
                    try
                    {
                        var json = File.ReadAllText(file);
                        var profile = JsonSerializer.Deserialize<SpoofingProfile>(json, _jsonOptions);
                        if (profile != null)
                        {
                            profiles.Add(profile);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Erreur lecture profil {file}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lecture dossier profils: {ex.Message}");
            }

            return profiles;
        }

        /// <summary>
        /// Supprime un profil
        /// </summary>
        public HardwareOperationResult DeleteProfile(string profileName)
        {
            try
            {
                var fileName = $"{SanitizeFileName(profileName)}.json";
                var filePath = Path.Combine(_profilesDirectory, fileName);

                if (!File.Exists(filePath))
                {
                    return HardwareOperationResult.CreateError($"Profil non trouvé: {profileName}");
                }

                File.Delete(filePath);
                return HardwareOperationResult.CreateSuccess($"Profil supprimé: {profileName}");
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur suppression profil: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Applique un profil au système
        /// </summary>
        public HardwareOperationResult ApplyProfile(SpoofingProfile profile, HwidManager hwidManager)
        {
            try
            {
                var results = new List<string>();
                var errors = new List<string>();

                // Appliquer les paramètres hardware
                foreach (var setting in profile.HardwareSettings)
                {
                    try
                    {
                        string valueToApply = setting.Value.SpoofType switch
                        {
                            SpoofType.Random => GenerateRandomValueForType(setting.Key),
                            SpoofType.Custom => setting.Value.Value,
                            SpoofType.Keep => "", // Ne pas modifier
                            _ => ""
                        };

                        if (!string.IsNullOrEmpty(valueToApply))
                        {
                            var result = hwidManager.ModifyHardwareId(setting.Key, valueToApply);
                            if (result.Success)
                            {
                                results.Add($"{setting.Key}: {result.Message}");
                            }
                            else
                            {
                                errors.Add($"{setting.Key}: {result.Message}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"{setting.Key}: {ex.Message}");
                    }
                }

                // TODO: Appliquer les paramètres USB
                // TODO: Appliquer les paramètres réseau

                var message = $"Profil '{profile.Name}' appliqué.\n" +
                             $"Succès: {results.Count}, Erreurs: {errors.Count}";

                if (errors.Count > 0)
                {
                    var result = HardwareOperationResult.CreateError(message);
                    foreach (var error in errors)
                    {
                        result.AddWarning(error);
                    }
                    return result;
                }

                return HardwareOperationResult.CreateSuccess(message);
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur application profil: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Génère une valeur aléatoire selon le type
        /// </summary>
        private string GenerateRandomValueForType(string hardwareType)
        {
            var random = new Random();
            
            return hardwareType.ToUpper() switch
            {
                var type when type.Contains("CPU") => GenerateRandomHex(16),
                var type when type.Contains("SERIAL") => GenerateRandomAlphaNumeric(10),
                var type when type.Contains("UUID") => Guid.NewGuid().ToString().ToUpper(),
                var type when type.Contains("MAC") => GenerateRandomMacAddress(),
                _ => GenerateRandomAlphaNumeric(8)
            };
        }

        /// <summary>
        /// Génère une chaîne hexadécimale aléatoire
        /// </summary>
        private string GenerateRandomHex(int length)
        {
            var random = new Random();
            var hex = new System.Text.StringBuilder();
            for (int i = 0; i < length; i++)
            {
                hex.Append(random.Next(0, 16).ToString("X"));
            }
            return hex.ToString();
        }

        /// <summary>
        /// Génère une chaîne alphanumérique aléatoire
        /// </summary>
        private string GenerateRandomAlphaNumeric(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var random = new Random();
            var result = new System.Text.StringBuilder();
            for (int i = 0; i < length; i++)
            {
                result.Append(chars[random.Next(chars.Length)]);
            }
            return result.ToString();
        }

        /// <summary>
        /// Génère une adresse MAC aléatoire
        /// </summary>
        private string GenerateRandomMacAddress()
        {
            var random = new Random();
            var mac = new byte[6];
            random.NextBytes(mac);
            mac[0] = (byte)(mac[0] & 0xFE); // Bit local admin à 0
            return string.Join(":", mac.Select(b => b.ToString("X2")));
        }

        /// <summary>
        /// Nettoie un nom de fichier
        /// </summary>
        private string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        }
    }

    /// <summary>
    /// Profil de spoofing
    /// </summary>
    public class SpoofingProfile
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime LastUsed { get; set; }
        public Dictionary<string, ProfileSetting> HardwareSettings { get; set; } = new();
        public Dictionary<string, UsbProfileSetting> UsbSettings { get; set; } = new();
        public Dictionary<string, NetworkProfileSetting> NetworkSettings { get; set; } = new();
    }

    /// <summary>
    /// Paramètre de profil
    /// </summary>
    public class ProfileSetting
    {
        public SpoofType SpoofType { get; set; }
        public string Value { get; set; } = string.Empty;
    }

    /// <summary>
    /// Paramètre USB de profil
    /// </summary>
    public class UsbProfileSetting
    {
        public string VendorId { get; set; } = string.Empty;
        public string ProductId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string SerialNumber { get; set; } = string.Empty;
    }

    /// <summary>
    /// Paramètre réseau de profil
    /// </summary>
    public class NetworkProfileSetting
    {
        public string MacAddress { get; set; } = string.Empty;
        public string AdapterName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Type de spoofing
    /// </summary>
    public enum SpoofType
    {
        Keep,       // Garder la valeur originale
        Random,     // Générer une valeur aléatoire
        Custom      // Utiliser une valeur personnalisée
    }
}
