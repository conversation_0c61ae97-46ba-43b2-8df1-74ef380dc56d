using System;
using System.Collections.Generic;
using System.Management;
using System.Text.RegularExpressions;
using Microsoft.Win32;
using HwidSpoofer.Models;

namespace HwidSpoofer.Services
{
    /// <summary>
    /// Service avancé de gestion des périphériques USB et leurs identifiants
    /// </summary>
    public class UsbDeviceManager
    {
        private readonly RegistryManager _registryManager;
        private readonly List<UsbDevice> _detectedDevices = new();

        public UsbDeviceManager(RegistryManager registryManager)
        {
            _registryManager = registryManager;
        }

        /// <summary>
        /// Détecte tous les périphériques USB connectés
        /// </summary>
        public List<UsbDevice> DetectUsbDevices()
        {
            _detectedDevices.Clear();

            try
            {
                // Détecter via WMI
                DetectUsbDevicesViaWmi();
                
                // Détecter via le registre (plus complet)
                DetectUsbDevicesViaRegistry();
                
                return new List<UsbDevice>(_detectedDevices);
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de la détection USB: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Détection via WMI (Windows Management Instrumentation)
        /// </summary>
        private void DetectUsbDevicesViaWmi()
        {
            using var searcher = new ManagementObjectSearcher(
                "SELECT * FROM Win32_PnPEntity WHERE DeviceID LIKE 'USB%'"
            );

            foreach (ManagementObject device in searcher.Get())
            {
                var deviceId = device["DeviceID"]?.ToString();
                var name = device["Name"]?.ToString();
                var description = device["Description"]?.ToString();

                if (!string.IsNullOrEmpty(deviceId))
                {
                    var usbDevice = ParseUsbDeviceId(deviceId, name, description);
                    if (usbDevice != null)
                    {
                        _detectedDevices.Add(usbDevice);
                    }
                }
            }
        }

        /// <summary>
        /// Détection via le registre Windows (plus détaillée)
        /// </summary>
        private void DetectUsbDevicesViaRegistry()
        {
            const string usbStorPath = @"SYSTEM\CurrentControlSet\Enum\USBSTOR";
            const string usbPath = @"SYSTEM\CurrentControlSet\Enum\USB";

            // Analyser USBSTOR (périphériques de stockage USB)
            AnalyzeRegistryPath(usbStorPath, "USBSTOR");
            
            // Analyser USB (tous les périphériques USB)
            AnalyzeRegistryPath(usbPath, "USB");
        }

        /// <summary>
        /// Analyse un chemin de registre pour les périphériques USB
        /// </summary>
        private void AnalyzeRegistryPath(string registryPath, string deviceType)
        {
            try
            {
                using var key = Registry.LocalMachine.OpenSubKey(registryPath);
                if (key == null) return;

                foreach (string subKeyName in key.GetSubKeyNames())
                {
                    using var deviceKey = key.OpenSubKey(subKeyName);
                    if (deviceKey == null) continue;

                    foreach (string instanceName in deviceKey.GetSubKeyNames())
                    {
                        using var instanceKey = deviceKey.OpenSubKey(instanceName);
                        if (instanceKey == null) continue;

                        var device = CreateUsbDeviceFromRegistry(
                            deviceType, subKeyName, instanceName, instanceKey
                        );
                        
                        if (device != null && !_detectedDevices.Exists(d => d.DeviceId == device.DeviceId))
                        {
                            _detectedDevices.Add(device);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur analyse registre {registryPath}: {ex.Message}");
            }
        }

        /// <summary>
        /// Crée un objet UsbDevice à partir des informations du registre
        /// </summary>
        private UsbDevice? CreateUsbDeviceFromRegistry(string deviceType, string subKeyName, 
            string instanceName, RegistryKey instanceKey)
        {
            try
            {
                var friendlyName = instanceKey.GetValue("FriendlyName")?.ToString();
                var deviceDesc = instanceKey.GetValue("DeviceDesc")?.ToString();
                var hardwareId = instanceKey.GetValue("HardwareID") as string[];
                var compatibleIds = instanceKey.GetValue("CompatibleIDs") as string[];

                var device = new UsbDevice
                {
                    DeviceType = deviceType,
                    DeviceId = $"{deviceType}\\{subKeyName}\\{instanceName}",
                    FriendlyName = friendlyName ?? "Périphérique Inconnu",
                    Description = deviceDesc ?? subKeyName,
                    InstanceId = instanceName,
                    RegistryPath = $@"SYSTEM\CurrentControlSet\Enum\{deviceType}\{subKeyName}\{instanceName}",
                    HardwareIds = hardwareId?.ToList() ?? new List<string>(),
                    CompatibleIds = compatibleIds?.ToList() ?? new List<string>()
                };

                // Parser les VID/PID si disponibles
                ParseVidPidFromHardwareId(device);

                return device;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Parse un Device ID USB pour extraire VID/PID
        /// </summary>
        private UsbDevice? ParseUsbDeviceId(string deviceId, string? name, string? description)
        {
            // Format typique: USB\VID_xxxx&PID_xxxx\instance
            var match = Regex.Match(deviceId, @"USB\\VID_([0-9A-F]{4})&PID_([0-9A-F]{4})\\(.+)");
            
            if (match.Success)
            {
                return new UsbDevice
                {
                    DeviceType = "USB",
                    DeviceId = deviceId,
                    VendorId = match.Groups[1].Value,
                    ProductId = match.Groups[2].Value,
                    InstanceId = match.Groups[3].Value,
                    FriendlyName = name ?? "Périphérique USB",
                    Description = description ?? "Périphérique USB",
                    RegistryPath = $@"SYSTEM\CurrentControlSet\Enum\USB\VID_{match.Groups[1].Value}&PID_{match.Groups[2].Value}\{match.Groups[3].Value}"
                };
            }

            return null;
        }

        /// <summary>
        /// Parse les VID/PID depuis les Hardware IDs
        /// </summary>
        private void ParseVidPidFromHardwareId(UsbDevice device)
        {
            foreach (var hardwareId in device.HardwareIds)
            {
                var match = Regex.Match(hardwareId, @"VID_([0-9A-F]{4}).*PID_([0-9A-F]{4})");
                if (match.Success)
                {
                    device.VendorId = match.Groups[1].Value;
                    device.ProductId = match.Groups[2].Value;
                    break;
                }
            }
        }

        /// <summary>
        /// Modifie les identifiants d'un périphérique USB
        /// </summary>
        public HardwareOperationResult ModifyUsbDevice(UsbDevice device, string? newVid = null, 
            string? newPid = null, string? newSerial = null)
        {
            try
            {
                var modifications = new List<string>();

                // Modifier VID si spécifié
                if (!string.IsNullOrEmpty(newVid) && newVid != device.VendorId)
                {
                    var result = ModifyRegistryValue(device.RegistryPath, "VendorId", newVid);
                    if (!result.Success) return result;
                    modifications.Add($"VID: {device.VendorId} -> {newVid}");
                    device.VendorId = newVid;
                }

                // Modifier PID si spécifié
                if (!string.IsNullOrEmpty(newPid) && newPid != device.ProductId)
                {
                    var result = ModifyRegistryValue(device.RegistryPath, "ProductId", newPid);
                    if (!result.Success) return result;
                    modifications.Add($"PID: {device.ProductId} -> {newPid}");
                    device.ProductId = newPid;
                }

                // Modifier le numéro de série si spécifié
                if (!string.IsNullOrEmpty(newSerial))
                {
                    var result = ModifyRegistryValue(device.RegistryPath, "SerialNumber", newSerial);
                    if (!result.Success) return result;
                    modifications.Add($"Serial: -> {newSerial}");
                }

                var message = modifications.Count > 0 
                    ? $"Périphérique USB modifié: {string.Join(", ", modifications)}"
                    : "Aucune modification appliquée";

                return HardwareOperationResult.CreateSuccess(message);
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur modification USB: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Génère des identifiants USB aléatoires valides
        /// </summary>
        public (string vid, string pid) GenerateRandomUsbIds()
        {
            var random = new Random();
            
            // Générer VID (éviter les VID réservés)
            var vid = random.Next(0x1000, 0xFFFF).ToString("X4");
            
            // Générer PID
            var pid = random.Next(0x0001, 0xFFFF).ToString("X4");
            
            return (vid, pid);
        }

        /// <summary>
        /// Restaure les identifiants originaux d'un périphérique
        /// </summary>
        public HardwareOperationResult RestoreUsbDevice(UsbDevice device)
        {
            try
            {
                // Restaurer depuis la sauvegarde du registre
                var result = _registryManager.RestoreRegistryValue(device.RegistryPath, "VendorId");
                if (!result.Success) return result;

                result = _registryManager.RestoreRegistryValue(device.RegistryPath, "ProductId");
                if (!result.Success) return result;

                result = _registryManager.RestoreRegistryValue(device.RegistryPath, "SerialNumber");
                if (!result.Success) return result;

                return HardwareOperationResult.CreateSuccess($"Périphérique USB restauré: {device.FriendlyName}");
            }
            catch (Exception ex)
            {
                return HardwareOperationResult.CreateError($"Erreur restauration USB: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Méthode helper pour modifier une valeur de registre
        /// </summary>
        private HardwareOperationResult ModifyRegistryValue(string registryPath, string keyName, string value)
        {
            return _registryManager.SetRegistryValue($"HKEY_LOCAL_MACHINE\\{registryPath}", keyName, value);
        }
    }

    /// <summary>
    /// Modèle représentant un périphérique USB
    /// </summary>
    public class UsbDevice
    {
        public string DeviceType { get; set; } = string.Empty;
        public string DeviceId { get; set; } = string.Empty;
        public string VendorId { get; set; } = string.Empty;
        public string ProductId { get; set; } = string.Empty;
        public string InstanceId { get; set; } = string.Empty;
        public string FriendlyName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string RegistryPath { get; set; } = string.Empty;
        public List<string> HardwareIds { get; set; } = new();
        public List<string> CompatibleIds { get; set; } = new();
        public bool IsModified { get; set; } = false;
        public DateTime LastModified { get; set; } = DateTime.MinValue;

        public string DisplayName => $"{FriendlyName} (VID:{VendorId}, PID:{ProductId})";
        
        public override string ToString() => DisplayName;
    }
}
