# Guide de Compilation - HWID Spoofer

## 🛠️ Prérequis

### Système d'exploitation
- **Windows 10** ou supérieur (recommandé : Windows 11)
- **Privilèges administrateur** requis pour l'exécution

### Outils de développement
- **Visual Studio 2022** (Community, Professional ou Enterprise)
  - Workload : ".NET Desktop Development"
  - Composant : ".NET 6.0 Runtime"
- **OU** Visual Studio Code avec :
  - Extension C# Dev Kit
  - .NET 6.0 SDK

### Dépendances .NET
- **.NET 6.0 SDK** ou supérieur
- **Windows Forms** (inclus dans .NET 6.0)
- **Packages NuGet** (installés automatiquement) :
  - `System.Management` v7.0.2
  - `Microsoft.Win32.Registry` v5.0.0
  - `Microsoft.VisualBasic` v10.3.0

## 📥 Installation

### 1. <PERSON><PERSON><PERSON> le projet
```bash
git clone <repository-url>
cd spoofer
```

### 2. Restaurer les packages NuGet
```bash
dotnet restore HwidSpoofer.sln
```

### 3. Vérifier la configuration
```bash
dotnet build HwidSpoofer.sln --configuration Debug
```

## 🔨 Compilation

### Compilation Debug (développement)
```bash
# Via .NET CLI
dotnet build HwidSpoofer.sln --configuration Debug

# Via Visual Studio
# Ouvrir HwidSpoofer.sln
# Build > Build Solution (Ctrl+Shift+B)
```

### Compilation Release (production)
```bash
# Via .NET CLI
dotnet build HwidSpoofer.sln --configuration Release

# Optimisations activées
dotnet publish HwidSpoofer/HwidSpoofer.csproj -c Release -o ./publish
```

### Compilation avec optimisations avancées
```bash
# Publication autonome (self-contained)
dotnet publish HwidSpoofer/HwidSpoofer.csproj \
  --configuration Release \
  --runtime win-x64 \
  --self-contained true \
  --output ./publish-standalone \
  /p:PublishSingleFile=true \
  /p:IncludeNativeLibrariesForSelfExtract=true
```

## 🚀 Exécution

### Mode Développement
```bash
# Depuis le répertoire source
dotnet run --project HwidSpoofer/HwidSpoofer.csproj

# Ou directement l'exécutable
./HwidSpoofer/bin/Debug/net6.0-windows/HwidSpoofer.exe
```

### Mode Production
```bash
# Exécutable compilé
./publish/HwidSpoofer.exe

# ⚠️ IMPORTANT : Lancer en tant qu'administrateur
# Clic droit > "Exécuter en tant qu'administrateur"
```

## 🔧 Configuration de l'IDE

### Visual Studio 2022
1. **Ouvrir** `HwidSpoofer.sln`
2. **Définir** le projet de démarrage : `HwidSpoofer`
3. **Configuration** : Debug ou Release
4. **Plateforme** : Any CPU
5. **Propriétés du projet** :
   - Target Framework : net6.0-windows
   - Output Type : WinExe
   - Use Windows Forms : true

### Visual Studio Code
1. **Ouvrir** le dossier racine
2. **Installer** les extensions recommandées
3. **Configuration** dans `.vscode/launch.json` :
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch HWID Spoofer",
            "type": "coreclr",
            "request": "launch",
            "program": "${workspaceFolder}/HwidSpoofer/bin/Debug/net6.0-windows/HwidSpoofer.exe",
            "args": [],
            "cwd": "${workspaceFolder}",
            "console": "internalConsole",
            "stopAtEntry": false,
            "requireExactSource": false
        }
    ]
}
```

## 🧪 Tests et Validation

### Tests de compilation
```bash
# Vérifier la syntaxe
dotnet build --verbosity normal

# Analyser les avertissements
dotnet build --verbosity detailed | grep warning
```

### Tests d'exécution
1. **Lancer** en mode administrateur
2. **Vérifier** l'initialisation du gestionnaire HWID
3. **Tester** la détection hardware
4. **Valider** les fonctions de sauvegarde/restauration

### Tests de sécurité
```bash
# Analyser les dépendances
dotnet list package --vulnerable

# Vérifier les permissions
# L'application doit demander les privilèges administrateur
```

## 📦 Packaging et Distribution

### Création d'un installateur
```bash
# Utiliser un outil comme Inno Setup ou WiX Toolset
# Exemple avec dotnet publish :

dotnet publish HwidSpoofer/HwidSpoofer.csproj \
  --configuration Release \
  --runtime win-x64 \
  --self-contained true \
  --output ./dist \
  /p:PublishSingleFile=true \
  /p:IncludeNativeLibrariesForSelfExtract=true \
  /p:PublishTrimmed=false
```

### Structure de distribution recommandée
```
HwidSpoofer-v1.0/
├── HwidSpoofer.exe          # Exécutable principal
├── README.md                # Documentation utilisateur
├── LEGAL.md                 # Mentions légales
├── LICENSE                  # Licence du logiciel
└── Documentation/
    ├── BUILD.md             # Ce fichier
    ├── TECHNICAL.md         # Documentation technique
    └── EXAMPLES.md          # Exemples d'usage
```

## 🐛 Dépannage

### Erreurs de compilation courantes

#### "Privilèges administrateur requis"
```bash
# Solution : Lancer Visual Studio en tant qu'administrateur
# Ou compiler depuis une invite de commande administrateur
```

#### "Package non trouvé"
```bash
# Restaurer les packages NuGet
dotnet restore --force
dotnet clean
dotnet build
```

#### "Framework non trouvé"
```bash
# Installer .NET 6.0 SDK
winget install Microsoft.DotNet.SDK.6
# Ou télécharger depuis https://dotnet.microsoft.com/download
```

### Erreurs d'exécution courantes

#### "Accès refusé au registre"
- **Cause** : Application non lancée en tant qu'administrateur
- **Solution** : Clic droit > "Exécuter en tant qu'administrateur"

#### "WMI non accessible"
- **Cause** : Service WMI désactivé
- **Solution** : `services.msc` > Windows Management Instrumentation > Démarrer

#### "Erreur de détection hardware"
- **Cause** : Pilotes manquants ou hardware non standard
- **Solution** : Mettre à jour les pilotes système

## 📊 Optimisations de Performance

### Compilation optimisée
```xml
<!-- Dans HwidSpoofer.csproj -->
<PropertyGroup Condition="'$(Configuration)'=='Release'">
  <Optimize>true</Optimize>
  <DebugType>none</DebugType>
  <DebugSymbols>false</DebugSymbols>
  <TrimMode>link</TrimMode>
</PropertyGroup>
```

### Réduction de la taille
```bash
# Publication avec trimming
dotnet publish -c Release -r win-x64 \
  /p:PublishSingleFile=true \
  /p:PublishTrimmed=true \
  /p:TrimMode=link
```

## 🔒 Considérations de Sécurité

### Signature de code
```bash
# Signer l'exécutable avec un certificat valide
signtool sign /f certificate.pfx /p password /t http://timestamp.server HwidSpoofer.exe
```

### Analyse antivirus
- **Prévoir** des faux positifs dus à la nature de l'outil
- **Soumettre** à VirusTotal pour analyse
- **Documenter** les détections comme faux positifs

### Protection contre la rétro-ingénierie
```bash
# Utiliser un obfuscateur .NET si nécessaire
# Exemple : ConfuserEx, .NET Reactor, etc.
```

---

**Note importante :** Ce logiciel nécessite des privilèges élevés pour fonctionner. Assurez-vous de comprendre les implications de sécurité avant la compilation et la distribution.
