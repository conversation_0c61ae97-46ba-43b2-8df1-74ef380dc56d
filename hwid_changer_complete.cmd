@echo off
setlocal enabledelayedexpansion

:: ========================================
::    HWID CHANGER COMPLET - CMD VERSION
:: ========================================
:: ATTENTION: Utilisation éducative uniquement
:: Modifie TOUS les identifiants hardware
:: ========================================

title HWID Changer Complet - Outil Educatif

:: Couleurs pour l'affichage
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "MAGENTA=[95m"
set "CYAN=[96m"
set "WHITE=[97m"
set "RESET=[0m"

echo %CYAN%========================================%RESET%
echo %CYAN%    HWID CHANGER COMPLET v2.0%RESET%
echo %CYAN%========================================%RESET%
echo.
echo %RED%ATTENTION: Outil educatif uniquement%RESET%
echo %RED%L'utilisation malveillante est interdite%RESET%
echo.

:: Vérifier les privilèges administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo %RED%ERREUR: Privileges administrateur requis%RESET%
    echo %YELLOW%Clic droit sur le script ^> "Executer en tant qu'administrateur"%RESET%
    pause
    exit /b 1
)

echo %GREEN%✓ Privileges administrateur detectes%RESET%
echo.

:: Avertissement final
echo %YELLOW%========================================%RESET%
echo %YELLOW%         AVERTISSEMENT FINAL%RESET%
echo %YELLOW%========================================%RESET%
echo.
echo %RED%Ce script va modifier TOUS vos identifiants:%RESET%
echo %WHITE%• Machine GUID%RESET%
echo %WHITE%• Nom d'ordinateur%RESET%
echo %WHITE%• Product ID Windows%RESET%
echo %WHITE%• Adresses MAC%RESET%
echo %WHITE%• Identifiants de disques%RESET%
echo %WHITE%• Identifiants USB%RESET%
echo %WHITE%• Proprietaire enregistre%RESET%
echo.
echo %RED%CES MODIFICATIONS SONT PERMANENTES !%RESET%
echo.
set /p "confirm=Etes-vous sur de vouloir continuer ? (OUI/non): "
if /i not "%confirm%"=="OUI" (
    echo %YELLOW%Operation annulee par l'utilisateur%RESET%
    pause
    exit /b 0
)

echo.
echo %CYAN%========================================%RESET%
echo %CYAN%    DEBUT DES MODIFICATIONS%RESET%
echo %CYAN%========================================%RESET%
echo.

:: Créer un dossier de sauvegarde
set "BACKUP_DIR=%USERPROFILE%\Desktop\HWID_Backup_%DATE:~-4%%DATE:~3,2%%DATE:~0,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%"
set "BACKUP_DIR=%BACKUP_DIR: =%"
mkdir "%BACKUP_DIR%" 2>nul

echo %BLUE%Dossier de sauvegarde: %BACKUP_DIR%%RESET%
echo.

:: ========================================
:: 1. MACHINE GUID
:: ========================================
echo %MAGENTA%[1/8] Modification du Machine GUID...%RESET%

:: Sauvegarder la valeur originale
for /f "tokens=3" %%a in ('reg query "HKLM\SOFTWARE\Microsoft\Cryptography" /v MachineGuid 2^>nul') do (
    echo %%a > "%BACKUP_DIR%\original_machine_guid.txt"
    echo %YELLOW%   Sauvegarde: %%a%RESET%
)

:: Générer un nouveau GUID
powershell -Command "$guid = [System.Guid]::NewGuid().ToString().ToUpper(); Write-Output $guid" > temp_guid.txt
set /p NEW_GUID=<temp_guid.txt
del temp_guid.txt

:: Appliquer le nouveau GUID
reg add "HKLM\SOFTWARE\Microsoft\Cryptography" /v MachineGuid /t REG_SZ /d "%NEW_GUID%" /f >nul 2>&1
if %errorLevel% equ 0 (
    echo %GREEN%   ✓ Machine GUID modifie: %NEW_GUID%%RESET%
) else (
    echo %RED%   ✗ Echec modification Machine GUID%RESET%
)

:: ========================================
:: 2. NOM D'ORDINATEUR
:: ========================================
echo %MAGENTA%[2/8] Modification du nom d'ordinateur...%RESET%

:: Sauvegarder
for /f "tokens=3" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName" /v ComputerName 2^>nul') do (
    echo %%a > "%BACKUP_DIR%\original_computer_name.txt"
    echo %YELLOW%   Sauvegarde: %%a%RESET%
)

:: Générer un nouveau nom
set /a "RAND_NUM=%RANDOM% %% 900000 + 100000"
set "NEW_COMPUTER_NAME=DESKTOP-%RAND_NUM%"

:: Appliquer le nouveau nom
reg add "HKLM\SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName" /v ComputerName /t REG_SZ /d "%NEW_COMPUTER_NAME%" /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\ComputerName\ActiveComputerName" /v ComputerName /t REG_SZ /d "%NEW_COMPUTER_NAME%" /f >nul 2>&1
if %errorLevel% equ 0 (
    echo %GREEN%   ✓ Nom d'ordinateur modifie: %NEW_COMPUTER_NAME%%RESET%
) else (
    echo %RED%   ✗ Echec modification nom d'ordinateur%RESET%
)

:: ========================================
:: 3. PRODUCT ID WINDOWS
:: ========================================
echo %MAGENTA%[3/8] Modification du Product ID Windows...%RESET%

:: Sauvegarder
for /f "tokens=3" %%a in ('reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion" /v ProductId 2^>nul') do (
    echo %%a > "%BACKUP_DIR%\original_product_id.txt"
    echo %YELLOW%   Sauvegarde: %%a%RESET%
)

:: Générer un nouveau Product ID
set /a "P1=%RANDOM% %% 90000 + 10000"
set /a "P2=%RANDOM% %% 90000 + 10000"
set /a "P3=%RANDOM% %% 90000 + 10000"
set /a "P4=%RANDOM% %% 90000 + 10000"
set "NEW_PRODUCT_ID=%P1%-%P2%-%P3%-%P4%"

:: Appliquer le nouveau Product ID
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion" /v ProductId /t REG_SZ /d "%NEW_PRODUCT_ID%" /f >nul 2>&1
if %errorLevel% equ 0 (
    echo %GREEN%   ✓ Product ID modifie: %NEW_PRODUCT_ID%%RESET%
) else (
    echo %RED%   ✗ Echec modification Product ID%RESET%
)

:: ========================================
:: 4. PROPRIETAIRE ENREGISTRE
:: ========================================
echo %MAGENTA%[4/8] Modification du proprietaire enregistre...%RESET%

:: Sauvegarder
for /f "tokens=3*" %%a in ('reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion" /v RegisteredOwner 2^>nul') do (
    echo %%a %%b > "%BACKUP_DIR%\original_registered_owner.txt"
    echo %YELLOW%   Sauvegarde: %%a %%b%RESET%
)

:: Générer un nouveau propriétaire
set /a "USER_NUM=%RANDOM% %% 9000 + 1000"
set "NEW_OWNER=User%USER_NUM%"

:: Appliquer le nouveau propriétaire
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion" /v RegisteredOwner /t REG_SZ /d "%NEW_OWNER%" /f >nul 2>&1
if %errorLevel% equ 0 (
    echo %GREEN%   ✓ Proprietaire modifie: %NEW_OWNER%%RESET%
) else (
    echo %RED%   ✗ Echec modification proprietaire%RESET%
)

:: ========================================
:: 5. ADRESSES MAC
:: ========================================
echo %MAGENTA%[5/8] Modification des adresses MAC...%RESET%

:: Créer un fichier de sauvegarde pour les MAC
echo Adresses MAC originales: > "%BACKUP_DIR%\original_mac_addresses.txt"

:: Parcourir les adaptateurs réseau
set "MAC_COUNT=0"
for /f "tokens=1" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}" /s /f "DriverDesc" 2^>nul ^| findstr "HKEY"') do (
    set "ADAPTER_KEY=%%a"
    
    :: Vérifier si c'est un vrai adaptateur (pas virtuel)
    for /f "tokens=3*" %%b in ('reg query "!ADAPTER_KEY!" /v DriverDesc 2^>nul ^| findstr "DriverDesc"') do (
        set "DRIVER_DESC=%%b %%c"
        echo !DRIVER_DESC! | findstr /i /v "virtual loopback bluetooth" >nul
        if !errorLevel! equ 0 (
            :: Sauvegarder l'adresse MAC originale
            for /f "tokens=3" %%d in ('reg query "!ADAPTER_KEY!" /v NetworkAddress 2^>nul') do (
                echo !ADAPTER_KEY! - %%d >> "%BACKUP_DIR%\original_mac_addresses.txt"
            )
            
            :: Générer une nouvelle adresse MAC
            call :GenerateMAC NEW_MAC
            
            :: Appliquer la nouvelle adresse MAC
            reg add "!ADAPTER_KEY!" /v NetworkAddress /t REG_SZ /d "!NEW_MAC!" /f >nul 2>&1
            if !errorLevel! equ 0 (
                echo %GREEN%   ✓ MAC modifiee: !NEW_MAC! ^(!DRIVER_DESC!^)%RESET%
                set /a "MAC_COUNT+=1"
            )
        )
    )
    
    :: Limiter à 3 adaptateurs pour éviter les problèmes
    if !MAC_COUNT! geq 3 goto :EndMAC
)
:EndMAC

:: ========================================
:: 6. IDENTIFIANTS DE DISQUES
:: ========================================
echo %MAGENTA%[6/8] Modification des identifiants de disques...%RESET%

:: Créer un fichier de sauvegarde pour les disques
echo Identifiants de disques originaux: > "%BACKUP_DIR%\original_disk_ids.txt"

:: Modifier les identifiants dans USBSTOR
set "DISK_COUNT=0"
for /f "tokens=1" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Enum\USBSTOR" 2^>nul ^| findstr "HKEY"') do (
    for /f "tokens=1" %%b in ('reg query "%%a" 2^>nul ^| findstr "HKEY"') do (
        :: Sauvegarder le nom original
        for /f "tokens=3*" %%c in ('reg query "%%b" /v FriendlyName 2^>nul') do (
            echo %%b - %%c %%d >> "%BACKUP_DIR%\original_disk_ids.txt"
            
            :: Générer un nouveau nom
            set /a "DISK_NUM=%RANDOM% %% 9000 + 1000"
            set "NEW_DISK_NAME=USB Storage Device !DISK_NUM!"
            
            :: Appliquer le nouveau nom
            reg add "%%b" /v FriendlyName /t REG_SZ /d "!NEW_DISK_NAME!" /f >nul 2>&1
            if !errorLevel! equ 0 (
                echo %GREEN%   ✓ Disque modifie: !NEW_DISK_NAME!%RESET%
                set /a "DISK_COUNT+=1"
            )
        )
        
        if !DISK_COUNT! geq 5 goto :EndDisk
    )
)
:EndDisk

:: ========================================
:: 7. IDENTIFIANTS USB
:: ========================================
echo %MAGENTA%[7/8] Modification des identifiants USB...%RESET%

:: Créer un fichier de sauvegarde pour les USB
echo Identifiants USB originaux: > "%BACKUP_DIR%\original_usb_ids.txt"

:: Modifier les périphériques USB
set "USB_COUNT=0"
for /f "tokens=1" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Enum\USB" 2^>nul ^| findstr "VID_"') do (
    for /f "tokens=1" %%b in ('reg query "%%a" 2^>nul ^| findstr "HKEY"') do (
        :: Sauvegarder le nom original
        for /f "tokens=3*" %%c in ('reg query "%%b" /v DeviceDesc 2^>nul') do (
            echo %%b - %%c %%d >> "%BACKUP_DIR%\original_usb_ids.txt"
            
            :: Générer un nouveau nom
            set /a "USB_NUM=%RANDOM% %% 9000 + 1000"
            set "NEW_USB_NAME=USB Device !USB_NUM!"
            
            :: Appliquer le nouveau nom
            reg add "%%b" /v DeviceDesc /t REG_SZ /d "!NEW_USB_NAME!" /f >nul 2>&1
            if !errorLevel! equ 0 (
                echo %GREEN%   ✓ USB modifie: !NEW_USB_NAME!%RESET%
                set /a "USB_COUNT+=1"
            )
        )
        
        if !USB_COUNT! geq 10 goto :EndUSB
    )
)
:EndUSB

:: ========================================
:: 8. NETTOYAGE ET FINALISATION
:: ========================================
echo %MAGENTA%[8/8] Nettoyage et finalisation...%RESET%

:: Vider les caches DNS et ARP
ipconfig /flushdns >nul 2>&1
arp -d * >nul 2>&1

:: Redémarrer les services réseau
net stop "DNS Client" >nul 2>&1
net start "DNS Client" >nul 2>&1

echo %GREEN%   ✓ Caches vides et services redemarres%RESET%

:: ========================================
:: RESUME FINAL
:: ========================================
echo.
echo %CYAN%========================================%RESET%
echo %CYAN%         MODIFICATIONS TERMINEES%RESET%
echo %CYAN%========================================%RESET%
echo.
echo %GREEN%✓ Machine GUID: %NEW_GUID%%RESET%
echo %GREEN%✓ Nom ordinateur: %NEW_COMPUTER_NAME%%RESET%
echo %GREEN%✓ Product ID: %NEW_PRODUCT_ID%%RESET%
echo %GREEN%✓ Proprietaire: %NEW_OWNER%%RESET%
echo %GREEN%✓ Adresses MAC: %MAC_COUNT% modifiees%RESET%
echo %GREEN%✓ Disques: %DISK_COUNT% modifies%RESET%
echo %GREEN%✓ USB: %USB_COUNT% modifies%RESET%
echo.
echo %YELLOW%📁 Sauvegardes dans: %BACKUP_DIR%%RESET%
echo.
echo %RED%⚠️  REDEMARRAGE OBLIGATOIRE pour appliquer tous les changements%RESET%
echo.

:: Proposer le redémarrage
set /p "restart=Redemarrer maintenant ? (O/n): "
if /i "%restart%"=="O" (
    echo %YELLOW%Redemarrage en cours...%RESET%
    shutdown /r /t 10 /c "Redemarrage pour appliquer les modifications HWID"
) else (
    echo %YELLOW%Pensez a redemarrer manuellement pour finaliser les modifications%RESET%
)

echo.
pause
goto :EOF

:: ========================================
:: FONCTIONS
:: ========================================

:GenerateMAC
:: Génère une adresse MAC aléatoire valide
set "MAC_CHARS=0123456789ABCDEF"
set "RESULT="
for /l %%i in (1,1,12) do (
    set /a "INDEX=!RANDOM! %% 16"
    for %%j in (!INDEX!) do set "RESULT=!RESULT!!MAC_CHARS:~%%j,1!"
)
:: S'assurer que le premier octet est pair (adresse unicast)
set "FIRST_CHAR=!RESULT:~0,1!"
if "!FIRST_CHAR!"=="1" set "RESULT=0!RESULT:~1!"
if "!FIRST_CHAR!"=="3" set "RESULT=2!RESULT:~1!"
if "!FIRST_CHAR!"=="5" set "RESULT=4!RESULT:~1!"
if "!FIRST_CHAR!"=="7" set "RESULT=6!RESULT:~1!"
if "!FIRST_CHAR!"=="9" set "RESULT=8!RESULT:~1!"
if "!FIRST_CHAR!"=="B" set "RESULT=A!RESULT:~1!"
if "!FIRST_CHAR!"=="D" set "RESULT=C!RESULT:~1!"
if "!FIRST_CHAR!"=="F" set "RESULT=E!RESULT:~1!"
set "%~1=!RESULT!"
goto :EOF
